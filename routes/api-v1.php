<?php

use App\Http\Controllers\API\V1\AuthController;
use App\Http\Controllers\API\V1\CategoryController;
use App\Http\Controllers\API\V1\ContactController;
use App\Http\Controllers\API\V1\ContentController;
use App\Http\Controllers\API\V1\MediaController;
use App\Http\Controllers\API\V1\PagesController;
use App\Http\Controllers\API\V1\QuestionController;
use App\Http\Controllers\API\V1\SchoolController;
use App\Http\Controllers\API\V1\UserController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('login', [AuthController::class, 'login']);
Route::post('register', [AuthController::class, 'register']);

Route::post('forget-password', [AuthController::class, 'forgetPassword']);
Route::post('verify-code', [AuthController::class, 'verifyCode']);

Route::group(['middleware' => ['auth:sanctum', 'check.active.school']], function () {
    Route::post('reset-password', [AuthController::class, 'resetPassword']);
    Route::get('schools', [SchoolController::class, 'index']);
    Route::get('teachers', [UserController::class, 'TeachersIndex']);
    Route::get('students', [UserController::class, 'StudentsIndex']);
    Route::get('main-content', [CategoryController::class, 'index']);
    Route::get('content', [ContentController::class, 'index']);
    Route::post('send-message', [ContactController::class, 'store']);

    // Media routes - إدارة الوسائط المحسنة
    Route::prefix('media')->group(function () {
        Route::post('upload', [MediaController::class, 'uploadImages']);
        Route::delete('delete', [MediaController::class, 'deleteImage']);
        Route::get('content/{contentId}', [MediaController::class, 'getContentMedia']);
        Route::post('optimize', [MediaController::class, 'optimizeExistingImages']);
    });

    // Questions routes - إدارة الأسئلة والأجوبة
    Route::prefix('questions')->group(function () {
        Route::get('content_id/{contentId}', [QuestionController::class, 'index']); // عرض جميع أسئلة المحتوى
        Route::post('/create-question', [QuestionController::class, 'store']); // إنشاء سؤال جديد
        Route::get('/{questionId}', [QuestionController::class, 'show']); // عرض سؤال محدد
        Route::post('/update-question', [QuestionController::class, 'update']); // تحديث سؤال
        Route::post('/add-ai-answer/{question_id}', [QuestionController::class, 'ai_answer']); // توليد إجابة بالذكاء الاصطناعي
        Route::patch('/{questionId}', [QuestionController::class, 'update']); // تحديث جزئي
        // Route::delete('/{questionId}', [QuestionController::class, 'destroy']); // حذف سؤال - معطل مؤقتاً
    });
});

Route::get('page/{slug}', [PagesController::class, 'page']);
