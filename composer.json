{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "barryvdh/laravel-translation-manager": "^0.6.6", "bensampo/laravel-enum": "^6.11", "beyondcode/laravel-websockets": "^1.14", "guzzlehttp/guzzle": "^7.2", "jantinnerezo/livewire-alert": "^3.0", "laravel/framework": "^10.10", "laravel/jetstream": "^2.1", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "laravolt/avatar": "^5.1", "livewire/livewire": "^2.11", "openai-php/client": "^0.14.0", "propaganistas/laravel-phone": "^5.2", "pusher/pusher-php-server": "7.0.2", "smalot/pdfparser": "^2.12", "spatie/laravel-data": "^4.5", "spatie/laravel-medialibrary": "^11.4", "spatie/laravel-sluggable": "^3.6", "spatie/laravel-translatable": "^6.6"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}