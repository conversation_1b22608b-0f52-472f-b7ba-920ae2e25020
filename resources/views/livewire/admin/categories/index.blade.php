<div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="category-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0 font-size-18">{{ __('categories') }}</h4>

                    <div class="category-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
                            <li class="breadcrumb-item active">{{ __('categories') }}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-end text-center">
                            <div class="col col-2">
                                <label class="form-check-label" for="select">{{ __('OrderBy') }}</label>
                                <select wire:model="orderBy" class="form-control mt-1">
                                    <option value="id">{{ __('id') }}</option>
                                    <option value="name">{{ __('Name') }}</option>
                                    @if ($trashed)
                                        <option value="deleted_at">{{ __('deleted_at') }}</option>
                                    @else
                                        <option value="created_at">{{ __('created_at') }}</option>
                                        <option value="updated_at">{{ __('updated_at') }}</option>
                                    @endif

                                </select>
                            </div>

                            <div class="col col-2">
                                <label class="form-check-label" for="select">{{ __('perPage') }}</label>
                                <select wire:model="perPage" class="form-control mt-1">
                                    <option value="10">10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                </select>
                            </div>

                            <div class="col col-2">
                                <label class="form-check-label" for="select">{{ __('SortBy') }}</label>
                                <select wire:model="sortBy" class="form-control mt-1">
                                    <option value="asc">{{ __('ASC') }}</option>
                                    <option value="desc">{{ __('DESC') }}</option>
                                </select>
                            </div>

                            <div class="form-check form-switch form-switch-lg">
                                <label class="form-check-label" for="trashed">{{ __('Show Trashed') }}</label>
                                <input type="checkbox" wire:model="trashed" value="true" id="customSwitchsizelg"
                                    class="form-check-input mt-1" />
                            </div>
                            <div class="col col-2">
                                <button type="button" class="btn btn-primary" wire:click="selectedItem('create',null)"
                                    wire:loading.attr="disabled">{{ __('Create New') }}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center align-item-middle">{{ __('ID') }}</th>
                            <th class="text-center align-item-middle">{{ __('title') }}</th>
                            <th class="text-center align-item-middle">{{ __('description') }}</th>
                            <th class="text-center align-item-middle">{{ __('Image') }}</th>
                            <th class="text-center align-item-middle">{{ __('AI Analysis') }}</th>
                            <th class="text-center align-item-middle">{{ __('AI Display') }}</th>
                            <th class="text-center align-item-middle">{{ __('Actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($categories as $category)
                            <tr>
                                <td
                                    class="text-center align-middle {{ $category->status == true ? 'bg-success' : '' }}">
                                    {{ $category->id }}</td>
                                <td
                                    class="text-center align-middle {{ $category->status == true ? 'bg-success' : '' }}">
                                    {{ $category->title }}</td>
                                <td
                                    class="text-center align-middle {{ $category->status == true ? 'bg-success' : '' }}">
                                    {{ $category->description }}</td>
                                <td
                                    class="text-center align-middle {{ $category->status == true ? 'bg-success' : '' }}">
                                    <div class="text-center mx-auto thumbnail" style="max-width: 100px;">
                                        @if ($media = $category->getFirstMedia('category'))
                                            <img class="img-thumbnail rounded mx-auto d-block"
                                                src="{{ route('media.show', $media->id) }}"
                                                alt="{{ $category->title }}">
                                        @else
                                            <p>No media available for this category.</p>
                                        @endif
                                    </div>
                                </td>
                                <td class="text-center align-middle {{ $category->status == true ? 'bg-success' : '' }}">
                                    @if($category->hasAiAnalysis())
                                        <span class="badge bg-warning">{{ __('Available') }}</span>
                                    @else
                                        <span class="badge bg-secondary">{{ __('Not Available') }}</span>
                                    @endif
                                </td>
                                <td class="text-center align-middle {{ $category->contents->has_ai_analysis == true ? 'bg-success' : '' }}">
                                    @if($category->contents->has_ai_analysis)
                                        <span class="badge bg-warning">{{ __('Available') }}</span>
                                    @else
                                        <span class="badge bg-secondary">{{ __('Not Available') }}</span>
                                    @endif
                                </td>
                                {{-- <td class="text-center">{{ $category->getMedia() }}</td> --}}
                                <td class="text-center align-middle" width="200">
                                    <div class="d-flex items-center justify-content-between">
                                        @if ($trashed)
                                            @can('restore', $category)
                                                <button wire:click="selectedItem('restore',{{ $category->id }})"
                                                    title="{{ __('restore') }}" class="btn btn-outline-warning">
                                                    <i class="fas fa-trash-restore"></i>
                                                </button>
                                            @endcan
                                            @can('forceDelete', $category)
                                                <button wire:click="selectedItem('forceDelete',{{ $category->id }})"
                                                    title="{{ __('force delete') }}" class="btn btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            @endcan
                                        @else
                                            @can('update', $category)
                                                <a href="{{ route('add.content', $category->id) }}"
                                                    title="{{ __('CreateContent') }}" class="btn btn-outline-success">
                                                    <i class="fas fa-plus"></i>
                                                </a>
                                            @endcan
                                            @if ($category->status == true)
                                                <button wire:click="selectedItem('deactivate',{{ $category->id }})"
                                                    title="{{ __('Deactivate') }}" class="btn btn-outline-danger">
                                                    <i class="fas fa-eye-slash"></i>
                                                </button>
                                            @else
                                                <button wire:click="selectedItem('activate',{{ $category->id }})"
                                                    title="{{ __('Activate') }}" class="btn btn-outline-success">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            @endif
                                            @if ($category->student_status == true)
                                                <button wire:click="selectedItem('deactivate_student',{{ $category->id }})"
                                                    title="{{ __('Deactivate') }}" class="btn btn-outline-danger">
                                                    <i class="fas fa-user-slash"></i>
                                                </button>
                                            @else
                                                <button wire:click="selectedItem('activate_student',{{ $category->id }})"
                                                    title="{{ __('Activate') }}" class="btn btn-outline-success">
                                                    <i class="fas fa-user"></i>
                                                </button>
                                            @endif
                                            @can('update', $category)
                                                <button wire:click="selectedItem('update',{{ $category->id }})"
                                                    title="{{ __('Edit') }}" class="btn btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            @endcan
                                            {{-- @can('update', $category)
                                                @if ($category->status == true)
                                                    <button wire:click="selectedItem('deactivate',{{ $category->id }})"
                                                        title="{{ __('Deactivate') }}" class="btn btn-outline-danger">
                                                        <i class="fas fa-eye-slash"></i>
                                                    </button>
                                                @else
                                                    <button wire:click="selectedItem('activate',{{ $category->id }})"
                                                        title="{{ __('Activate') }}" class="btn btn-outline-success">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                @endif
                                            @endcan
                                           @can('view', $category)
                                                <a href="{{ route('public.category',$category->slug) }}" target="_blank"
                                                    title="" class="btn btn-outline-info">
                                                    <i class="fas fa-chalkboard"></i>
                                                </a>
                                            @endcan --}}
                                            @can('delete', $category)
                                                <button wire:click="selectedItem('delete',{{ $category->id }})"
                                                    class="btn btn-outline-danger">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            @endcan
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td class="text-center" colspan="6">{{ __('No Data Found') }}</td>
                            </tr>
                        @endforelse


                    </tbody>
                </table>
                @if (!empty($categories))
                    <div class="dataTables_paginate paging_simple_numbers">
                        {{ $categories->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
</div>
</div>
<livewire:admin.categories.create />
<livewire:admin.categories.update />
<livewire:admin.categories.delete />
