<div>
    <div wire:model="showDeleteModel">
        @if ($showDeleteModel)
            <div class="modal fade show d-block modal-backdrop.show" id="deleteModal" tabindex="-1"
                aria-labelledby="deleteModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="deleteModalLabel">{{ __('Delete') }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                                wire:click="closeDeleteModel"></button>
                        </div>
                        <div class="modal-body">
                            <h6>{{ __('Delete question') }}</h6>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                wire:click="closeDeleteModel" wire:loading.attr="disabled">{{ __('Cancel') }}</button>
                            <button type="button" class="btn btn-danger" wire:click="delete"
                                wire:loading.attr="disabled">{{ __('Delete') }}</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-backdrop fade show"></div>
        @endif
    </div>
    <div wire:model="showRestoreModel">
        @if ($showRestoreModel)
            <div class="modal fade show d-block modal-backdrop.show" id="restoreModal" tabindex="-1"
                aria-labelledby="restoreModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="restoreModalLabel">{{ __('Restore') }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                                wire:click="closeRestoreModel"></button>
                        </div>
                        <div class="modal-body">
                            <h6>{{ __('Restore question') }}</h6>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                wire:click="closeRestoreModel" wire:loading.attr="disabled">{{ __('Cancel') }}</button>
                            <button type="button" class="btn btn-success" wire:click="restore"
                                wire:loading.attr="disabled">{{ __('Restore') }}</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-backdrop fade show"></div>
        @endif
    </div>
    <div wire:model="showForceDeleteModel">
        @if ($showForceDeleteModel)
            <div class="modal fade show d-block modal-backdrop.show" id="forceDeleteModal" tabindex="-1"
                aria-labelledby="forceDeleteModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="forceDeleteModalLabel">{{ __('Delete') }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                                wire:click="closeForceDeleteModel"></button>
                        </div>
                        <div class="modal-body">
                            <h6>{{ __('Force Delete question') }}</h6>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                wire:click="closeForceDeleteModel" wire:loading.attr="disabled">{{ __('Cancel') }}</button>
                            <button type="button" class="btn btn-danger" wire:click="forceDelete"
                                wire:loading.attr="disabled">{{ __('Delete') }}</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-backdrop fade show"></div>
        @endif
    </div>
    <div wire:model="showActivateVisibilityModel">
        @if ($showActivateVisibilityModel)
            <div class="modal fade show d-block modal-backdrop.show" id="activateVisibilityModal" tabindex="-1"
                aria-labelledby="activateVisibilityModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="activateVisibilityModalLabel">{{ __('ActivateVisibility') }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                                wire:click="closeActivateVisibilityModel"></button>
                        </div>
                        <div class="modal-body">
                            <h6>{{ __('Activate question') }}</h6>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                wire:click="closeActivateVisibilityModel" wire:loading.attr="disabled">{{ __('Cancel') }}</button>
                            <button type="button" class="btn btn-success" wire:click="ActivateVisibility"
                                wire:loading.attr="disabled">{{ __('Activate') }}</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-backdrop fade show"></div>
        @endif
    </div>
    <div wire:model="showDeactivateVisibilityModel">
        @if ($showDeactivateVisibilityModel)
            <div class="modal fade show d-block modal-backdrop.show" id="deactivateVisibilityModal" tabindex="-1"
                aria-labelledby="deactivateVisibilityModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="deactivateVisibilityModalLabel">{{ __('DeactivateVisibility') }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                                wire:click="closeDeactivateVisibilityModel"></button>
                        </div>
                        <div class="modal-body">
                            <h6>{{ __('Deactivate question') }}</h6>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                wire:click="closeDeactivateVisibilityModel" wire:loading.attr="disabled">{{ __('Cancel') }}</button>
                            <button type="button" class="btn btn-danger" wire:click="DeactivateVisibility"
                                wire:loading.attr="disabled">{{ __('Deactivate') }}</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-backdrop fade show"></div>
        @endif
    </div>
    <div wire:model="showActivateVisibilityModel_student">
        @if ($showActivateVisibilityModel_student)
            <div class="modal fade show d-block modal-backdrop.show" id="activateVisibilityModal" tabindex="-1"
                aria-labelledby="activateVisibilityModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="activateVisibilityModalLabel">{{ __('ActivateVisibility') }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                                wire:click="closeActivateVisibilityModel_student"></button>
                        </div>
                        <div class="modal-body">
                            <h6>{{ __('Activate question') }}</h6>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                wire:click="closeActivateVisibilityModel_student" wire:loading.attr="disabled">{{ __('Cancel') }}</button>
                            <button type="button" class="btn btn-success" wire:click="ActivateVisibility_student"
                                wire:loading.attr="disabled">{{ __('Activate') }}</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-backdrop fade show"></div>
        @endif
    </div>
    <div wire:model="showDeactivateVisibilityModel_student">
        @if ($showDeactivateVisibilityModel_student)
            <div class="modal fade show d-block modal-backdrop.show" id="deactivateVisibilityModal" tabindex="-1"
                aria-labelledby="deactivateVisibilityModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="deactivateVisibilityModalLabel">{{ __('DeactivateVisibility') }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                                wire:click="closeDeactivateVisibilityModel_student"></button>
                        </div>
                        <div class="modal-body">
                            <h6>{{ __('Deactivate question') }}</h6>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                wire:click="closeDeactivateVisibilityModel_student" wire:loading.attr="disabled">{{ __('Cancel') }}</button>
                            <button type="button" class="btn btn-danger" wire:click="DeactivateVisibility_student"
                                wire:loading.attr="disabled">{{ __('Deactivate') }}</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-backdrop fade show"></div>
        @endif
    </div>
</div>
