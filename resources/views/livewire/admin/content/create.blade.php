<div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="category-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0 font-size-18">{{ __('Content') }}</h4>

                    <div class="category-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
                            <li class="breadcrumb-item active">{{ __('Content') }}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                    </div>
                    <div class="card-body">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="createModalLabel">{{ __('Create') }} {{ __('Content') }}
                                </h5>
                            </div>
                            <div class="modal-body">

                                <div class="col col-12 mt-2">
                                    <label class="form-check-label" for="category">{{ __('MainContent') }}</label>
                                    <select id="category" wire:model.defer="category" class="form-control mt-1">
                                        <option value="null" disabled>{{ __('choose') }}</option>
                                        @forelse ($categories as $cat)
                                            <option value="{{ $cat->id }}">{{ $cat->title }}</option>
                                        @empty
                                            <option value="null" disabled selected>
                                                {{ __('Must_Add_Category_first') }}</option>
                                        @endforelse
                                    </select>
                                </div>
                                <div class="col col-12 mt-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" wire:model.defer="has_ai_analysis" id="has_ai_analysis">
                                        <label class="form-check-label" for="has_ai_analysis">
                                            {{ __('Has AI Analysis') }}
                                        </label>
                                    </div>
                                </div>
                                <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="pills-class-tab" data-bs-toggle="pill"
                                            data-bs-target="#pills-class" type="button" role="tab"
                                            aria-controls="pills-class" aria-selected="true">
                                            {{ __('Class') }}</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="pills-teacher-tab" data-bs-toggle="pill"
                                            data-bs-target="#pills-teacher" type="button" role="tab"
                                            aria-controls="pills-teacher" aria-selected="true">
                                            {{ __('Teacher') }}</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="pills-student-tab" data-bs-toggle="pill"
                                            data-bs-target="#pills-student" type="button" role="tab"
                                            aria-controls="pills-student" aria-selected="true">
                                            {{ __('Student') }}</button>
                                    </li>
                                </ul>
                                <div class="tab-content" id="pills-tabContent">
                                    <div class="tab-pane fade show active" id="pills-class" role="tabpanel"
                                        aria-labelledby="pills-class-tab">
                                        <div class="modal-body">
                                            <div class="col col-12 dropzone dz-clickable"
                                                style="max-height: 225px !important;overflow:hidden;">
                                                @if ($classMedia)
                                                    Photo Preview:
                                                    <img src="{{ $classMedia->temporaryUrl() }}" class="w-100">
                                                @else
                                                    <i class="fa fa-upload w-100 mediaClass"></i>
                                                @endif
                                                <label for="classMedia">{{ __('media') }}</label>
                                                <input type="file" wire:model.defer="classMedia" id="classMedia"
                                                    style="padding: 6%;cursor: pointer;opacity: 0;position: absolute;top: 62px;left: 0;width: 100%;">
                                                @error('classMedia')
                                                    <span class="error">{{ $message }}</span>
                                                @enderror
                                            </div>
                                            <div class="col col-12" wire:ignore>
                                                <label class="form-check-label" for="content.class">{{ __('content') }}
                                                    {{ __('Class') }}</label>
                                                <textarea wire:model.defer="content.class" id="content.class" type="text" class="form-control mt-1"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="tab-pane fade" id="pills-teacher" role="tabpanel"
                                        aria-labelledby="pills-teacher-tab">
                                        <div class="modal-body">
                                            <div class="col col-12 dropzone dz-clickable"
                                                style="max-height: 225px !important;overflow:hidden;">
                                                @if ($teacherMedia)
                                                    Photo Preview:
                                                    <img src="{{ $teacherMedia->temporaryUrl() }}" class="w-100">
                                                @else
                                                    <i class="fa fa-upload w-100 mediaClass"></i>
                                                @endif
                                                <label for="teacherMedia">{{ __('media') }}</label>
                                                <input type="file" wire:model.defer="teacherMedia" id="teacherMedia"
                                                    style="padding: 6%;cursor: pointer;opacity: 0;position: absolute;top: 62px;left: 0;width: 100%;">
                                                @error('teacherMedia')
                                                    <span class="error">{{ $message }}</span>
                                                @enderror
                                            </div>
                                            <div class="col col-12" wire:ignore>
                                                <label class="form-check-label"
                                                    for="content.teacher">{{ __('content') }}
                                                    {{ __('Teacher') }}</label>
                                                <textarea wire:model.defer="content.teacher" id="content.teacher" type="text" class="form-control mt-1"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="pills-student" role="tabpanel"
                                        aria-labelledby="pills-student-tab">
                                        <div class="modal-body">
                                            <div class="col col-12 dropzone dz-clickable"
                                                style="max-height: 225px !important;overflow:hidden;">
                                                @if ($studentMedia)
                                                    Photo Preview:
                                                    <img src="{{ $studentMedia->temporaryUrl() }}" class="w-100">
                                                @else
                                                    <i class="fa fa-upload w-100 mediaClass"></i>
                                                @endif
                                                <label for="studentMedia">{{ __('media') }}</label>
                                                <input type="file" wire:model.defer="studentMedia"
                                                    id="studentMedia"
                                                    style="padding: 6%;cursor: pointer;opacity: 0;position: absolute;top: 62px;left: 0;width: 100%;">
                                                @error('studentMedia')
                                                    <span class="error">{{ $message }}</span>
                                                @enderror
                                            </div>
                                            <div class="col col-12" wire:ignore>
                                                <label class="form-check-label"
                                                    for="content.student">{{ __('content') }}
                                                    {{ __('Student') }}</label>
                                                <textarea wire:model.defer="content.student" id="content.student" type="text" class="form-control mt-1"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                    wire:click="closeCreateModel"
                                    wire:loading.attr="disabled">{{ __('Cancel') }}</button>
                                <button type="button" class="btn btn-primary" wire:click="create"
                                    wire:loading.attr="disabled">{{ __('Save') }}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <style>
        .mediaClass {
            width: 100% !important;
            text-align: center;
            font-size: 150px;
            padding-top: 40px;
            padding-bottom: 40px;
        }
    </style>
    <script>
        document.addEventListener('livewire:load', function() {
            initializeSummernote();
        });

        function initializeSummernote() {
            const editorElements = ['#content\\.class', '#content\\.teacher', '#content\\.student'];
            editorElements.forEach((selector) => {
                if ($(selector).data('initialized')) {
                    $(selector).summernote('destroy');
                }
                $(selector).summernote({
                    height: 400,
                    minHeight: 300,
                    maxHeight: 400,
                    focus: true,
                    tabsize: 1,
                    callbacks: {
                        onChange: function(contents, $editable) {
                            Livewire.emit('updateContent', {
                                id: selector,
                                content: contents
                            });
                        },
                        onImageUpload: function(files) {
                            uploadImage(files[0], $(this));
                        }
                    }
                });
                $(selector).data('initialized', true);
            });
        }


        function uploadImage(file, editor) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            fetch('/upload-editor-image', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.url) {
                    editor.summernote('insertImage', data.url);
                } else {
                    alert('خطأ في رفع الصورة: ' + (data.error || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('خطأ في رفع الصورة');
            });
        }

        document.addEventListener('DOMContentLoaded', function () {
    const activeTab = localStorage.getItem('activeTab') || '#pills-class'; // التاب الافتراضي

    // استعادة التاب النشط عند تحميل الصفحة
    document.querySelector(activeTab).classList.add('show', 'active');

    document.querySelectorAll('[data-bs-toggle="pill"]').forEach((tabButton) => {
        tabButton.addEventListener('click', function () {
            localStorage.setItem('activeTab', tabButton.getAttribute('data-bs-target'));
        });
    });
});

    </script>
</div>
