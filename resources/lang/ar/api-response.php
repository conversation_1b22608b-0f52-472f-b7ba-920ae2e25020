<?php

return array (
  'mobile exists error' => 'رقم الجوال موجود بالفعل',
  'forbidden' => 'عذراً!!، ليس لديك صلاحيات للقيام بهذا',
  'unable to upload file' => 'يرجى المحاولة لاحقا. يوجد خطأ غير معروف',
  'not approved yet' => 'لا يمكن استعمال التطبيق الأن يرجى إنتظار موافقة الإدارة',
  'no driver found' => 'يرجى المحاولة لاحقا لا يوجد سائق متاح حاليا',
  'operation_succeeded' => 'نجحت العملية.',
  'operation_failed' => 'فشلت العملية.',
  'missing_parameter' => 'يوجد نقص في البيانات.',
  'data_not_found' => 'لم يتم العثور على بيانات.',
  'already_exists' => 'موجود مسبقا.',
  'unauthenticated' => '.غير مصدق',
  'unauthorized' => '.غير مصرح',
  'invalid_data' => 'بيانات غير صالحة.',
  'server_error' => 'خطأ في الخادم.',
  'MainContent_required' => 'يجب اختيار المحتوى الرئيسي',
  'invaled_data' => 'اسم المستخدم أو كلمة المرور خاطئة',
  'verification_code_invalid' => 'رمز التحقق غير صحيح',
  'verification_code_expired' => 'انتهت صلاحية رمز التحقق',

  // Questions API messages
  'question_created_successfully' => 'تم إنشاء السؤال بنجاح',
  'question_updated_successfully' => 'تم تحديث السؤال بنجاح',
  'question_deleted_successfully' => 'تم حذف السؤال بنجاح',
  'question_not_found' => 'السؤال غير موجود',
  'questions_retrieved_successfully' => 'تم استرجاع الأسئلة بنجاح',
  
  // School status messages
  'school_not_active' => 'المدرسة غير مفعلة أو انتهت صلاحية الاشتراك',
);
