<?php

/**
 * اختبار متعدد لأنواع مختلفة من الأسئلة
 */

$baseUrl = 'http://localhost/SchoolManagement';

// حالات اختبار مختلفة
$testCases = [
    [
        'name' => 'سؤال مرتبط مباشرة بالمحتوى',
        'data' => [
            'content_id' => 1,
            'question' => 'ما هي قواعد اللغة العربية الأساسية؟',
            'difficulty' => 'متوسط'
        ]
    ],
    [
        'name' => 'سؤال عام تعليمي',
        'data' => [
            'content_id' => 1,
            'question' => 'كيف يمكن تحسين مهارات التعلم؟',
            'difficulty' => 'سهل'
        ]
    ],
    [
        'name' => 'سؤال بصيغة مختلفة',
        'data' => [
            'content_id' => 1,
            'question' => 'اشرح أهمية التعليم في بناء المجتمع',
            'difficulty' => 'صعب'
        ]
    ],
    [
        'name' => 'سؤال قصير',
        'data' => [
            'content_id' => 1,
            'question' => 'ما هو التعليم؟',
            'difficulty' => 'سهل'
        ]
    ],
    [
        'name' => 'سؤال مع مولد بالذكاء الاصطناعي',
        'data' => [
            'content_id' => 1,
            'question' => 'سؤال تجريبي مولد بالذكاء الاصطناعي',
            'is_ai_generated' => true
        ]
    ]
];

echo "🧪 اختبار متعدد لـ API إنشاء الأسئلة\n";
echo "=====================================\n\n";

$successCount = 0;
$totalTests = count($testCases);

foreach ($testCases as $index => $testCase) {
    echo "📝 اختبار " . ($index + 1) . ": " . $testCase['name'] . "\n";
    echo "السؤال: " . $testCase['data']['question'] . "\n";
    
    // إنشاء طلب cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . '/api/v1/create-question');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testCase['data']));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ خطأ في cURL: $error\n";
    } else {
        $responseData = json_decode($response, true);
        
        if ($httpCode === 200 && isset($responseData['success']) && $responseData['success']) {
            echo "✅ نجح الاختبار\n";
            $successCount++;
        } else {
            echo "❌ فشل الاختبار\n";
            if (isset($responseData['message'])) {
                echo "   الرسالة: " . $responseData['message'] . "\n";
            }
        }
    }
    
    echo "---\n\n";
    
    // توقف قصير بين الطلبات
    sleep(1);
}

echo "📊 ملخص النتائج:\n";
echo "================\n";
echo "نجح: $successCount من $totalTests\n";
echo "معدل النجاح: " . round(($successCount / $totalTests) * 100, 2) . "%\n";

if ($successCount === $totalTests) {
    echo "🎉 جميع الاختبارات نجحت!\n";
} elseif ($successCount > $totalTests / 2) {
    echo "✅ معظم الاختبارات نجحت\n";
} else {
    echo "⚠️ تحتاج لمزيد من التحسينات\n";
}

?>
