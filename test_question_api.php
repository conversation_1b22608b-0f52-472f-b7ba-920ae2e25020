<?php

/**
 * ملف اختبار بسيط لـ API إنشاء الأسئلة
 * يمكن تشغيله من سطر الأوامر لاختبار التحسينات
 */

// تحديد المسار الأساسي للمشروع
$baseUrl = 'http://localhost/SchoolManagement'; // غير هذا حسب إعداد الخادم المحلي

// بيانات اختبار
$testData = [
    'content_id' => 1, // تأكد من وجود محتوى بهذا المعرف
    'question' => 'ما هي أهمية التعليم في المجتمع؟',
    'difficulty' => 'متوسط',
    'type' => 'عام',
    'is_ai_generated' => false
];

// رؤوس HTTP
$headers = [
    'Content-Type: application/json',
    'Accept: application/json',
    // 'Authorization: Bearer YOUR_TOKEN_HERE' // أضف التوكن إذا كان مطلوباً
];

echo "🧪 اختبار API إنشاء الأسئلة\n";
echo "================================\n\n";

echo "📝 بيانات الاختبار:\n";
echo json_encode($testData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// إنشاء طلب cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/api/v1/create-question');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

echo "🚀 إرسال الطلب...\n";

// تنفيذ الطلب
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

echo "📊 النتائج:\n";
echo "HTTP Code: $httpCode\n";

if ($error) {
    echo "❌ خطأ في cURL: $error\n";
} else {
    echo "✅ الاستجابة:\n";
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
        // تحليل النتيجة
        if ($httpCode === 200 && isset($responseData['success']) && $responseData['success']) {
            echo "\n🎉 تم إنشاء السؤال بنجاح!\n";
        } else {
            echo "\n⚠️  فشل في إنشاء السؤال:\n";
            if (isset($responseData['message'])) {
                echo "الرسالة: " . $responseData['message'] . "\n";
            }
        }
    } else {
        echo "استجابة غير صالحة: $response\n";
    }
}

echo "\n================================\n";
echo "انتهى الاختبار\n";

?>
