<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

class Category extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use HasTranslations;
    use SoftDeletes;

    public $translatable = ['title', 'description'];

    protected $fillable = [
        'title',
        'description',
        'status',
        'student_status',
    ];

    public function contents()
    {
        return $this->hasOne(Content::class, 'category_id', 'id');
    }

    public function getImageAttribute()
    {
        return $this->getMedia('category');
    }

    /**
     * Check if this category has AI analysis through its content.
     */
    public function hasAiAnalysis()
    {
        return $this->contents()->whereHas('aiAnalysis')->exists();
    }

    /**
     * Get AI analysis status as text.
     */
    public function getAiAnalysisStatusAttribute()
    {
        return $this->hasAiAnalysis() ? 'متوفر' : 'غير متوفر';
    }
}
