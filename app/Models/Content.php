<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

// use Spatie\Translatable\HasTranslations;

class Content extends Model implements HasMedia
{
    use HasFactory;
    // use HasTranslations;
    use InteractsWithMedia;
    // public $translatable = ['anything'];
    public $fillable = [
        'category_id',
        'has_ai_analysis',
        'description',
    ];

    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('class_media');
        $this->addMediaCollection('teacher_media');
        $this->addMediaCollection('student_media');
    }

    public function aiAnalysis()
    {
        return $this->hasOne(AiAnalysis::class);
    }

    /**
     * Get all questions for this content.
     */
    public function questionsAnswers()
    {
        return $this->hasMany(QuestionAnswer::class);
    }

    /**
     * Get only AI-generated questions for this content.
     */
    public function aiGeneratedQuestions()
    {
        return $this->hasMany(QuestionAnswer::class)
            ->where('is_ai_generated', true);
    }

    /**
     * Get only teacher-created questions for this content.
     */
    public function teacherQuestions()
    {
        return $this->hasMany(QuestionAnswer::class)
            ->where('is_ai_generated', false);
    }
}
