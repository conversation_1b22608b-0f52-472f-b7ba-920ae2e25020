<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class QuestionAnswer extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $table = 'questions_answers';

    protected $fillable = [
        'user_id',
        'content_id',
        'question',
        'answer',
        'difficulty',
        'type',
        'status',
        'is_ai_generated'
    ];

    protected $casts = [
        'status' => 'boolean',
        'is_ai_generated' => 'boolean'
    ];

    public function content()
    {
        return $this->belongsTo(Content::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope للأسئلة المفعلة فقط
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope للأسئلة غير المفعلة
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * Scope للأسئلة الخاصة بمعلم معين
     */
    public function scopeByTeacher($query, $teacherId)
    {
        return $query->where('user_id', $teacherId);
    }

    /**
     * حفظ المرفقات في collection مخصص
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('question_attachments')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/jpg', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);
    }
}
