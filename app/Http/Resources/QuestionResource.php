<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QuestionResource extends JsonResource
{
    public static function collection($resource)
    {
        $resource->loadMissing(['media']);
        return parent::collection($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'content_id' => $this->content_id,
            'question' => $this->question,
            'answer' => $this->answer,
            'difficulty' => $this->difficulty,
            // 'difficulty_label' => $this->getDifficultyLabel(),
            'type' => $this->type,
            'is_ai_generated' => $this->is_ai_generated,
            'is_ai_generated_label' => $this->is_ai_generated ? 'مولد بالذكاء الاصطناعي' : 'مضاف يدوياً',
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'created_at_human' => HumanDateResource::make($this->created_at),
            'updated_at_human' => HumanDateResource::make($this->updated_at),

            // معلومات إضافية
            'content' => $this->whenLoaded('content', function () {
                return [
                    'id' => $this->content->id,
                    'name' => $this->content->name,
                ];
            }),

            'files' => $this->whenLoaded('media', function () {
                return $this->media->map(function ($media) {
                    return [
                        'id' => $media->id,
                        'name' => $media->name,
                        'url' => $media->getUrl(),
                        'mime_type' => $media->mime_type,
                    ];
                });
            }),
        ];
    }

    /*
     * Get difficulty label in Arabic.
     */
    // private function getDifficultyLabel(): string
    // {
    //     return match ($this->difficulty) {
    //         'سهل' => 'سهل',
    //         'متوسط' => 'متوسط',
    //         'صعب' => 'صعب',
    //         default => 'غير محدد'
    //     };
    // }
}
