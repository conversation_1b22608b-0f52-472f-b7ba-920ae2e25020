<?php

namespace App\Http\Resources;

use App\Models\AiPermission;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class ContentResource extends JsonResource
{
    private function checkPermission(string $permission): bool
    {
        $user = Auth::user();

        // السماح للمستخدم ذو الرول 1 بكل شيء
        if ($user && $user->role_id === 1) {
            return true;
        }

        // التحقق من صلاحيات المعلم
        if ($user && $user->type === 2) {
            $permissions = AiPermission::where(function ($query) use ($user) {
                $query->where('teacher_id', $user->id)
                    ->orWhere('school_id', $user->school_id);
            })->first();

            return $permissions && $permissions->$permission;
        }

        return false;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // تحويل حقل الوصف إلى مصفوفة
        $description = json_decode($this->description, true);

        return [
            'id' => $this->id,
            'main_content' => ShortCategoryResource::make($this->category),
            'content_class' => $description['class'] ?? '',
            'content_teacher' => $description['teacher'] ?? '',
            'has_ai_analysis' => ((bool) $this->has_ai_analysis),
            'ai_analysis' => ((bool) $this->has_ai_analysis) ? $this->when(
                $this->checkPermission('can_view_analysis'),
                fn () => $this->aiAnalysis ? AiAnalysisResource::make($this->aiAnalysis) : null
            ) : null,
            'questions_answers' => $this->when(
                $this->checkPermission('can_view_questions'),
                function () {
                    if (!$this->questionsAnswers) {
                        return null;
                    }

                    $currentUserId = Auth::id();
                    $filteredQuestions = $this->questionsAnswers->filter(function ($question) use ($currentUserId) {
                        // إظهار الأسئلة المفعلة من جميع المستخدمين
                        if ($question->status == 1) {
                            return true;
                        }

                        // إظهار الأسئلة غير المفعلة فقط إذا كان المستخدم الحالي هو من أضافها
                        return $question->user_id == $currentUserId;
                    });

                    return QuestionResource::collection($filteredQuestions);
                }
            ),
            'content_student' => $description['student'] ?? '',
            'classMedia' => $this->getMedia('class_media')->map(function ($media) {
                return [
                    'id' => $media->id,
                    // 'url' => Auth::check() ? $media->getFullUrl() : null,
                    'url' => url("/file/{$media->id}.{$media->extension}"),
                ];
            }),
            'teacherMedia' => $this->getMedia('teacher_media')->map(function ($media) {
                return [
                    'id' => $media->id,
                    // 'url' => Auth::check() ? $media->getFullUrl() : null,
                    'url' => url("/file/{$media->id}.{$media->extension}"),
                ];
            }),
            'studentMedia' => $this->getMedia('student_media')->map(function ($media) {
                return [
                    'id' => $media->id,
                    // 'url' => Auth::check() ? $media->getFullUrl() : null,
                    'url' => url("/file/{$media->id}.{$media->extension}"),
                ];
            }),
            'created_at' => HumanDateResource::make($this->created_at),
        ];
    }
}
