<?php

namespace App\Http\Resources;

use App\Models\AiPermission;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $user = Auth::user();
        $schoolStatus = $user->school;

        if ($schoolStatus) {
            if ($schoolStatus->status == 0) {
                $schoolStatus->status_text = 'not_active';
                $schoolStatus->can_access = false;
            } elseif (now()->greaterThan($schoolStatus->expired_at)) {
                $schoolStatus->status_text = 'subscription_ended';
                $schoolStatus->can_access = false;
            } else {
                $schoolStatus->status_text = 'active';
                $schoolStatus->can_access = true;
            }
        }
        
        // جلب أذونات الذكاء الاصطناعي للمستخدم
        $aiPermissions = null;
        if ($this->type == 2) { // إذا كان المستخدم معلم
            $aiPermissions = AiPermission::where(function($query) {
                $query->where('teacher_id', $this->id)
                      ->orWhere('school_id', $this->school_id);
            })->first();
        }

        return [
            'id' => $this->id,
            'name' => $this->name,
            'username' => $this->username,
            'school_id' => $user->school->id ?? '',
            'phone' => $this->phone,
            'email' => $this->email,
            'status' => $schoolStatus->status_text ?? 'unknown',
            'can_access' => $schoolStatus->can_access ?? false,
            'school_expired_at' => $schoolStatus->expired_at ?? null,
            'type' => $this->type === 2 ? 'Teacher' : ($this->type === 3 ? 'Student' : ''), // 2 for teacher, 1 for student
            'ai_permissions' => $aiPermissions ? [
                'can_view_analysis' => (bool)$aiPermissions->can_view_analysis,
                'can_view_questions' => (bool)$aiPermissions->can_view_questions,
                'can_add_questions' => (bool)$aiPermissions->can_add_questions,
                'can_edit_questions' => (bool)$aiPermissions->can_edit_questions,
                'can_delete_questions' => (bool)$aiPermissions->can_delete_questions,
                'can_generate_questions' => (bool)$aiPermissions->can_generate_questions,
                'can_generate_answers' => (bool)$aiPermissions->can_generate_answers,
                'questions_limit' => (int)$aiPermissions->questions_limit,
            ] : null,
        ];
    }
}
