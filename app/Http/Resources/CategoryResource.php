<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            // 'media' => $this->getFirstMediaUrl('category'),
            'media' => $this->media->first() ? url("/file/{$this->media->first()->id}.{$this->media->first()->extension}") : null,
            'has_ai_analysis' => $this->hasAiAnalysis(),
            'created_at' => HumanDateResource::make($this->created_at),        ];
    }
}
