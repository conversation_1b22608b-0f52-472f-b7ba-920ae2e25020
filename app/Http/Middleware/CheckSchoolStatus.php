<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Helpers\ResponseHelper;

class CheckSchoolStatus
{
    public function handle(Request $request, Closure $next)
    {
        if (Auth::check()) {
            $user = Auth::user();
            $school = $user->school;
            
            if ($school) {
                $isActive = $school->status == 1 && now()->lessThanOrEqualTo($school->expired_at);
                
                if (!$isActive) {
                    // حذف جميع التوكنات للمستخدم
                    $user->tokens()->delete();
                    
                    // تسجيل خروج المستخدم
                    Auth::logout();
                    
                    return ResponseHelper::authenticationFailed(__('api-response.school_not_active'));
                }
            }
        }
        
        return $next($request);
    }
}