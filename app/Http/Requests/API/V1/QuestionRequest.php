<?php

namespace App\Http\Requests\API\V1;

use Illuminate\Foundation\Http\FormRequest;

class QuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // سيتم التحقق من الصلاحيات في ContentService
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'content_id' => 'required|exists:contents,id', // جعل content_id مطلوب
            'question_id' => 'nullable|exists:questions_answers,id',
            'question' => 'required|string|min:5|max:1000', // جعل question مطلوب
            'answer' => 'nullable|string|min:1|max:5000',
            'difficulty' => 'nullable|in:سهل,متوسط,صعب',
            'type' => 'nullable|string|max:100',
            'is_ai_generated' => 'nullable|boolean',
            'attachment' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx|max:10240', // دعم مزيد من أنواع الملفات
        ];

        // للتحديث، جعل بعض الحقول اختيارية
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $rules['content_id'] = 'nullable|exists:contents,id';
            $rules['question'] = 'nullable|string|min:5|max:1000';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'question.required' => 'السؤال مطلوب',
            'question.min' => 'السؤال يجب أن يكون على الأقل 5 أحرف',
            'question.max' => 'السؤال يجب أن لا يتجاوز 1000 حرف',
            'answer.required' => 'الإجابة مطلوبة',
            'answer.min' => 'الإجابة يجب أن تكون على الأقل 5 أحرف',
            'answer.max' => 'الإجابة يجب أن لا تتجاوز 5000 حرف',
            'difficulty.in' => 'مستوى الصعوبة يجب أن يكون: سهل، متوسط، أو صعب',
            'type.max' => 'نوع السؤال يجب أن لا يتجاوز 100 حرف',
            'is_ai_generated.boolean' => 'حقل الذكاء الاصطناعي يجب أن يكون true أو false',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'question' => 'السؤال',
            'answer' => 'الإجابة',
            'difficulty' => 'مستوى الصعوبة',
            'type' => 'نوع السؤال',
            'is_ai_generated' => 'مولد بالذكاء الاصطناعي',
        ];
    }
}
