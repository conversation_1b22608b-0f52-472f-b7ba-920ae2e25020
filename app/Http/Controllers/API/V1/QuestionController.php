<?php

namespace App\Http\Controllers\API\V1;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\V1\QuestionRequest;
use App\Http\Resources\QuestionResource;
use App\Services\ContentService;

class QuestionController extends Controller
{
    public function __construct(protected ContentService $contentService)
    {
    }

    /**
     * عرض جميع أسئلة المحتوى.
     */
    public function index($contentId)
    {
        try {
            $serviceResult = $this->contentService->getContentQuestions($contentId);

            if ($serviceResult->isSuccess) {
                return ResponseHelper::operationSucceeded(
                    QuestionResource::collection($serviceResult->data),
                    __('api-response.questions_retrieved_successfully')
                );
            }

            return ResponseHelper::operationFailed($serviceResult->message ?? __('api-response.operation_failed'));
        } catch (\Exception $e) {
            return ResponseHelper::operationFailed($e->getMessage());
        }
    }

    /**
     * إنشاء سؤال جديد.
     */
    public function store(QuestionRequest $request)
    {
        try {
            $data = $request->validated();
            $attachment = $request->hasFile('attachment') ? $request->file('attachment') : null;

            $contentId = $data['content_id'];
            if (!$contentId) {
                return ResponseHelper::operationFailed('المحتوى غير موجود');
            }

            $serviceResult = $this->contentService->addQuestion($contentId, $data, $attachment);

            if ($serviceResult->isSuccess) {
                return ResponseHelper::operationSucceeded(
                    new QuestionResource($serviceResult->data),
                    __('api-response.question_created_successfully')
                );
            }

            return ResponseHelper::operationFailed($serviceResult->message ?? __('api-response.operation_failed'));
        } catch (\Exception $e) {
            \Log::error('QuestionController store error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return ResponseHelper::operationFailed($e->getMessage());
        }
    }

    /**
     * عرض سؤال محدد.
     */
    public function show($questionId, $contentId = null)
    {
        try {
            if (!$this->contentService->checkPermission('can_view_questions')) {
                return ResponseHelper::operationFailed('ليس لديك صلاحية لعرض الأسئلة');
            }

            $question = \App\Models\QuestionAnswer::findOrFail($questionId);

            return ResponseHelper::operationSucceeded(
                new QuestionResource($question),
                __('api-response.operation_succeeded')
            );
        } catch (\Exception $e) {
            return ResponseHelper::operationFailed($e->getMessage());
        }
    }

    /**
     * تحديث سؤال موجود.
     */
    public function update(QuestionRequest $request)
    {
        try {
            $data = $request->validated();
            $questionId = $data['question_id'];
            if (!$questionId) {
                return ResponseHelper::operationFailed('السؤال غير موجود');
            }
            $serviceResult = $this->contentService->editQuestion($questionId, $data);

            if ($serviceResult->isSuccess) {
                return ResponseHelper::operationSucceeded(
                    new QuestionResource($serviceResult->data),
                    __('api-response.question_updated_successfully')
                );
            }

            return ResponseHelper::operationFailed($serviceResult->message ?? __('api-response.operation_failed'));
        } catch (\Exception $e) {
            return ResponseHelper::operationFailed($e->getMessage());
        }
    }

    /**
     * توليد إجابة بالذكاء الاصطناعي.
     */
    public function ai_answer($question_id)
    {
        try {
            // التحقق من وجود السؤال أولاً
            $question = \App\Models\QuestionAnswer::find($question_id);
            if (!$question) {
                return ResponseHelper::operationFailed('السؤال غير موجود');
            }

            $serviceResult = $this->contentService->ai_answer($question_id);

            if ($serviceResult->isSuccess) {
                return ResponseHelper::operationSucceeded(
                    new QuestionResource($serviceResult->data),
                    'تم توليد الإجابة بنجاح'
                );
            }

            return ResponseHelper::operationFailed($serviceResult->message ?? 'فشل في توليد الإجابة');
        } catch (\Exception $e) {
            return ResponseHelper::operationFailed($e->getMessage());
        }
    }

    /**
     * حذف سؤال - معطل مؤقتاً.
     */
    public function destroy($contentId, $questionId)
    {
        return ResponseHelper::operationFailed('وظيفة الحذف معطلة مؤقتاً');
    }
}
