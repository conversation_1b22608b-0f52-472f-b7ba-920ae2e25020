<?php

namespace App\Http\Controllers\API\V1;

use App\Exceptions\VerificationCodeExpiredException;
use App\Exceptions\VerificationCodeNotApprovedException;
use App\Helpers\ResponseHelper;
use App\Helpers\ServiceResult;
use App\Http\Controllers\Controller;
use App\Http\Requests\ForgetPasswordRequest;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\RegisterRequest;
use App\Http\Requests\ResetPasswordRequest;
use App\Http\Requests\VerifycodeRequest;
use App\Http\Resources\UserResource;
use App\Services\AuthService;

class AuthController extends Controller
{
    public function __construct(protected AuthService $authService)
    {
    }

    public function login(LoginRequest $request)
    {
        /**
         * @var array
         */
        $data = $request->validated();

        /**
         * @var ServiceResult
         */
        $service_result = $this->authService->login($data);

        if (!$service_result->isSuccess) {
            $message = $service_result->message ?? __('api-response.invaled_data');
            return ResponseHelper::authenticationFailed($message);
        }
        $service_result->data['user'] = UserResource::make($service_result->data['user']);

        return ResponseHelper::operationSucceeded($service_result->data, __('api-response.operation_succeeded'));
    }

    public function register(RegisterRequest $request)
    {
        /**
         * @var array
         */
        $data = $request->validated();

        /**
         * @var ServiceResult
         */
        $service_result = $this->authService->register($data);

        if (!$service_result->isSuccess) {
            return ResponseHelper::authenticationFailed(__('api-response.unauthenticated'));
        }

        $service_result->data['user'] = UserResource::make($service_result->data['user']);

        return ResponseHelper::operationSucceeded($service_result->data, __('api-response.operation_succeeded'));
    }

    public function forgetPassword(ForgetPasswordRequest $request)
    {
        /**
         * @var array
         */
        $data = $request->validated();

        /**
         * @var ServiceResult
         */
        $service_result = $this->authService->sendCode($data);

        if (!$service_result->isSuccess) {
            return ResponseHelper::operationFailed(__('api-response.operation_failed'));
        }

        return ResponseHelper::operationSucceeded(null, __('api-response.operation_succeeded'));
    }

    public function verifyCode(VerifycodeRequest $request)
    {
        /**
         * @var array
         */
        $data = $request->validated();

        try {
            $service_result = $this->authService->verifyCode($data);

            if (!$service_result->isSuccess) {
                return ResponseHelper::authenticationFailed(__('api-response.operation_failed'));
            }
        } catch (VerificationCodeNotApprovedException $ex) {
            return ResponseHelper::authenticationFailed(__('api-response.verification_code_invalid'));
        } catch (VerificationCodeExpiredException $ex) {
            return ResponseHelper::authenticationFailed(__('api-response.verification_code_expired'));
        }

        return ResponseHelper::accepted($service_result->data, __('api-response.operation_succeeded'));
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        $new_password = $request->input('password');

        $service_result = $this->authService->resetPassword($new_password);

        if (!$service_result->isSuccess) {
            return ResponseHelper::operationFailed(__('api-response.operation_failed'));
        }

        return ResponseHelper::accepted($service_result->data, __('api-response.operation_succeeded'));
    }
}
