<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImageUploadController extends Controller
{
    public function upload(Request $request)
    {
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            
            // التحقق من نوع الملف
            if (!in_array($file->getMimeType(), ['image/jpeg', 'image/png', 'image/gif', 'image/webp'])) {
                return response()->json(['error' => 'نوع الملف غير مدعوم'], 400);
            }
            
            // التحقق من حجم الملف (5MB)
            if ($file->getSize() > 5 * 1024 * 1024) {
                return response()->json(['error' => 'حجم الملف كبير جداً'], 400);
            }
            
            // إنشاء اسم فريد
            $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
            $path = 'images/editor/' . $filename;
            
            // حفظ الملف
            Storage::disk('public')->put($path, file_get_contents($file));
            
            // إرجاع رابط الملف بالمسار الصحيح
            $url = asset('storage/' . $path);
            
            \Log::info('Image uploaded for editor', [
                'filename' => $filename,
                'path' => $path,
                'url' => $url
            ]);
            
            return response()->json(['url' => $url]);
        }
        
        return response()->json(['error' => 'لم يتم رفع أي ملف'], 400);
    }
}