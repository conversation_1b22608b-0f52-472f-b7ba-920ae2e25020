<?php

namespace App\Http\Controllers;

use App\Models\AiPermission;
use App\Models\AiUsageLog;
use App\Models\QuestionAnswer;
use App\Models\Content;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class QuestionController extends Controller
{
    /**
     * Store a newly created question in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'content_id' => 'required|exists:contents,id',
            'question' => 'required|string|max:1000',
            'answer' => 'nullable|string|max:2000',
            'question_attachments' => 'nullable|array',
            'question_attachments.*' => 'file|mimes:jpg,jpeg,png,pdf,doc,docx|max:10240',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $user = Auth::user();
            if ($user->type === 2) { // Teacher-specific checks
                $permission = AiPermission::where(function ($query) use ($user) {
                    $query->where('teacher_id', $user->id)
                        ->orWhere('school_id', $user->school_id);
                })->first();

                if (!$permission || !$permission->can_add_questions) {
                    return response()->json(['success' => false, 'message' => 'لا تملك صلاحية إضافة الأسئلة'], 403);
                }

                if ($permission->questions_limit > 0) {
                    if (!AiUsageLog::canPerformAction($user->id, 'add_questions', $permission->questions_limit)) {
                        $todayUsage = AiUsageLog::getTodayUsage($user->id, 'add_questions');
                        return response()->json(['success' => false, 'message' => "تم استنفاد الحد اليومي ({$todayUsage}/{$permission->questions_limit})"], 429);
                    }
                }
            }

            $isAiGenerated = $request->input('is_ai_generated') == 1;
            $attachments = $request->hasFile('question_attachments') ? $request->file('question_attachments') : [];

            // Skip relevance check if the question has attachments
            if (!$isAiGenerated && empty($attachments)) {
                $analysisResult = $this->analyzeQuestionRelevance($request->content_id, $request->question);
                if (!$analysisResult['is_relevant']) {
                    return response()->json(['success' => false, 'message' => 'السؤال غير مخصص لهذا المحتوى. يرجى إضافة سؤال متعلق بالمحتوى التعليمي المعروض.'], 400);
                }
            }

            $question = QuestionAnswer::create([
                'user_id' => $user->id,
                'content_id' => $request->content_id,
                'question' => $request->question,
                'answer' => $request->answer,
                'difficulty' => 'متوسط',
                'type' => $request->type ?? 'عام',
                'status' => $user->type === 2 ? 0 : 1, // Teachers: inactive, Admin: active
                'is_ai_generated' => $isAiGenerated,
            ]);

            // Attach files if they exist
            if (!empty($attachments)) {
                foreach ($attachments as $attachment) {
                    $question->addMedia($attachment)->toMediaCollection('question_attachments');
                }
            }

            if ($user->type === 2) {
                AiUsageLog::logUsage($user->id, 'add_questions');
            }

            return response()->json([
                'success' => true,
                'message' => 'تم إضافة السؤال بنجاح',
                'question' => $question->load('media'), // Return question with media
            ]);

        } catch (\Exception $e) {
            \Log::error('Error adding question (Web)', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json(['success' => false, 'message' => 'فشل في إضافة السؤال. يرجى المحاولة مرة أخرى.'], 500);
        }
    }

    /**
     * Update the specified question in storage.
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'question_id' => 'required|exists:questions_answers,id',
            'question' => 'required|string|max:1000',
            'answer' => 'nullable|string|max:2000',
            'new_attachments' => 'nullable|array',
            'new_attachments.*' => 'file|mimes:jpg,jpeg,png,pdf,doc,docx|max:10240',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        try {
            $question = QuestionAnswer::findOrFail($request->question_id);

            // Authorization Check
            if (Auth::id() !== $question->user_id && Auth::user()->role_id !== 1) {
                return response()->json(['success' => false, 'message' => 'غير مصرح لك بتحديث هذا السؤال.'], 403);
            }

            $question->update([
                'question' => $request->question,
                'answer' => $request->answer,
            ]);

            // Handle new attachments
            if ($request->hasFile('new_attachments')) {
                foreach ($request->file('new_attachments') as $attachment) {
                    $question->addMedia($attachment)->toMediaCollection('question_attachments');
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث السؤال بنجاح.',
                'data' => $question->load('media'),
            ]);

        } catch (\Exception $e) {
            \Log::error('Error updating question: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'فشل تحديث السؤال.'], 500);
        }
    }
    
    /**
     * Get attachments for a specific question
     */
    public function getAttachments($id)
    {
        try {
            $question = QuestionAnswer::findOrFail($id);
            
            // Authorization Check
            if (Auth::id() !== $question->user_id && Auth::user()->role_id !== 1) {
                return response()->json(['success' => false, 'message' => 'غير مصرح لك بعرض مرفقات هذا السؤال.'], 403);
            }
            
            $attachments = $question->getMedia('question_attachments')->map(function ($media) {
                return [
                    'id' => $media->id,
                    'name' => $media->name,
                    'file_name' => $media->file_name,
                    'mime_type' => $media->mime_type,
                    'size' => $media->size,
                    'url' => route('web.show', $media->id),
                ];
            });
            
            return response()->json([
                'success' => true,
                'attachments' => $attachments
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Error getting question attachments: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'فشل في جلب المرفقات.'], 500);
        }
    }

    /**
     * Remove the specified question from storage.
     */
    public function destroy(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'question_id' => 'required|exists:questions_answers,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $question = QuestionAnswer::findOrFail($request->question_id);

            // Check if the user has permission to delete this question
            //            if ($question->created_by != Auth::id()) {
            //                return response()->json([
            //                    'success' => false,
            //                    'message' => __('You are not authorized to delete this question.')
            //                ], 403);
            //            }

            $question->delete();

            return response()->json([
                'success' => true,
                'message' => __('Question deleted successfully'),
            ]);
        } catch (\Exception $e) {
            \Log::error('Error deleting question: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Failed to delete question. Please try again.'),
            ], 500);
        }
    }

    /**
     * تحليل مدى ارتباط السؤال بالمحتوى التعليمي
     */
    private function analyzeQuestionRelevance($contentId, $question)
    {
        try {
            $content = Content::with('aiAnalysis')->find($contentId);
            if (!$content) {
                return ['is_relevant' => false, 'reason' => 'المحتوى غير موجود'];
            }

            // الحصول على المحتوى من تحليل الذكاء الاصطناعي
            $textContent = '';
            
            if ($content->aiAnalysis) {
                // استخدام التحليل الشامل أولاً، ثم تحليل النص
                if (!empty($content->aiAnalysis->comprehensive_analysis)) {
                    $textContent = strip_tags($content->aiAnalysis->comprehensive_analysis);
                } elseif (!empty($content->aiAnalysis->text_analysis)) {
                    $textContent = strip_tags($content->aiAnalysis->text_analysis);
                }
            }

            // إذا لم يكن هناك محتوى، نقبل السؤال
            if (empty(trim($textContent))) {
                return ['is_relevant' => true, 'reason' => 'لا يوجد محتوى للمقارنة'];
            }

            // إنشاء prompt لتحليل الارتباط
            $prompt = "أنت خبير في تحليل المحتوى التعليمي. مهمتك هي تحديد ما إذا كان السؤال المقدم مرتبطاً بالمحتوى التعليمي أم لا.\n\n";
            $prompt .= "المحتوى التعليمي:\n" . trim($textContent) . "\n\n";
            $prompt .= "السؤال المراد تحليله: " . $question . "\n\n";
            $prompt .= "قم بتحليل ما إذا كان السؤال مرتبطاً بالمحتوى التعليمي المقدم. أجب بـ 'نعم' إذا كان السؤال مرتبطاً بالمحتوى، أو 'لا' إذا كان غير مرتبط.\n";
            $prompt .= "يجب أن يكون السؤال متعلقاً بالمفاهيم أو المعلومات أو الموضوعات الموجودة في المحتوى التعليمي.\n\n";
            $prompt .= "أجب فقط بكلمة واحدة: نعم أو لا";

            $response = $this->callGeminiAPI($prompt, 50);

            if ($response && $response !== 'فشل في الاتصال بخدمة التحليل. يرجى المحاولة لاحقاً.') {
                $isRelevant = (strpos(strtolower($response), 'نعم') !== false);
                return [
                    'is_relevant' => $isRelevant,
                    'reason' => $isRelevant ? 'السؤال مرتبط بالمحتوى' : 'السؤال غير مرتبط بالمحتوى'
                ];
            }

            // في حالة فشل التحليل، نقبل السؤال لتجنب منع المستخدمين
            return ['is_relevant' => true, 'reason' => 'فشل في التحليل - تم قبول السؤال'];

        } catch (\Exception $e) {
            \Log::error('Question relevance analysis error: ' . $e->getMessage());
            // في حالة الخطأ، نقبل السؤال لتجنب منع المستخدمين
            return ['is_relevant' => true, 'reason' => 'خطأ في التحليل - تم قبول السؤال'];
        }
    }

    /**
     * تحليل المرفقات مع السؤال لتوليد إجابة شاملة
     */
    public function generateAIAnswerWithAttachments(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'question' => 'required|string',
            'content_id' => 'required|exists:contents,id',
            'question_attachments' => 'nullable|array',
            'question_attachments.*' => 'file|mimes:jpg,jpeg,png,pdf,doc,docx|max:10240',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $question = $request->input('question');
            $contentId = $request->input('content_id');
            $attachments = $request->hasFile('question_attachments') ? $request->file('question_attachments') : [];

            // تحليل المحتوى الأساسي
            $content = Content::with('aiAnalysis')->find($contentId);
            $baseContent = '';
            
            if ($content && $content->aiAnalysis) {
                if (!empty($content->aiAnalysis->comprehensive_analysis)) {
                    $baseContent = strip_tags($content->aiAnalysis->comprehensive_analysis);
                } elseif (!empty($content->aiAnalysis->text_analysis)) {
                    $baseContent = strip_tags($content->aiAnalysis->text_analysis);
                }
            }

            // تحليل المرفقات
            $attachmentAnalysis = '';
            if (!empty($attachments)) {
                $attachmentAnalysis = $this->analyzeAttachments($attachments);
            }

            // إنشاء prompt شامل
            $prompt = $this->buildComprehensivePrompt($question, $baseContent, $attachmentAnalysis);
            
            // توليد الإجابة
            $answer = $this->callGeminiAPI($prompt, 500);

            if ($answer && $answer !== 'فشل في الاتصال بخدمة التحليل. يرجى المحاولة لاحقاً.') {
                return response()->json([
                    'success' => true,
                    'answer' => $answer,
                    'message' => 'تم توليد الإجابة بنجاح مع تحليل المرفقات'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'فشل في توليد الإجابة. يرجى المحاولة مرة أخرى.'
            ]);

        } catch (\Exception $e) {
            \Log::error('Error generating AI answer with attachments: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء توليد الإجابة'
            ], 500);
        }
    }

    /**
     * تحليل المرفقات
     */
    private function analyzeAttachments($attachments)
    {
        $analysis = '';
        
        foreach ($attachments as $attachment) {
            $mimeType = $attachment->getMimeType();
            
            if (strpos($mimeType, 'image/') === 0) {
                // تحليل الصور
                $imageAnalysis = $this->analyzeImage($attachment);
                $analysis .= "تحليل الصورة: " . $imageAnalysis . "\n";
            } elseif (strpos($mimeType, 'application/pdf') === 0) {
                // تحليل PDF
                $pdfText = $this->extractTextFromPDF($attachment);
                $analysis .= "محتوى PDF: " . $pdfText . "\n";
            } elseif (in_array($mimeType, ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])) {
                // تحليل Word documents
                $docText = $this->extractTextFromDoc($attachment);
                $analysis .= "محتوى المستند: " . $docText . "\n";
            }
        }
        
        return $analysis;
    }

    /**
     * تحليل الصور باستخدام Gemini Vision
     */
    private function analyzeImage($image)
    {
        try {
            $apiKey = env('GEMINI_API_KEY');
            if (empty($apiKey)) {
                return 'لا يمكن تحليل الصورة - مفتاح API غير متوفر';
            }

            // تحويل الصورة إلى base64
            $imageData = base64_encode(file_get_contents($image->getPathname()));
            $mimeType = $image->getMimeType();

            $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=' . $apiKey;
            
            $payload = [
                'contents' => [[
                    'parts' => [
                        ['text' => 'حلل هذه الصورة واستخرج أي نص أو معلومات مفيدة منها. اكتب التحليل باللغة العربية.'],
                        [
                            'inline_data' => [
                                'mime_type' => $mimeType,
                                'data' => $imageData
                            ]
                        ]
                    ]
                ]],
                'generationConfig' => ['temperature' => 0.3, 'maxOutputTokens' => 300]
            ];

            $response = Http::timeout(30)->post($url, $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                return $responseData['candidates'][0]['content']['parts'][0]['text'] ?? 'لا يمكن تحليل الصورة';
            }

            return 'فشل في تحليل الصورة';
        } catch (\Exception $e) {
            \Log::error('Image analysis error: ' . $e->getMessage());
            return 'خطأ في تحليل الصورة';
        }
    }

    /**
     * استخراج النص من PDF
     */
    private function extractTextFromPDF($pdf)
    {
        try {
            $parser = new \Smalot\PdfParser\Parser();
            $pdfParsed = $parser->parseFile($pdf->getPathname());
            $text = $pdfParsed->getText();
            
            // تنظيف النص وإزالة المسافات الزائدة
            $text = preg_replace('/\s+/', ' ', trim($text));
            
            return !empty($text) ? $text : 'لا يمكن استخراج النص من هذا الملف';
        } catch (\Exception $e) {
            \Log::error('PDF text extraction error: ' . $e->getMessage());
            return 'لا يمكن استخراج النص من PDF: ' . $e->getMessage();
        }
    }

    /**
     * استخراج النص من مستندات Word
     */
    private function extractTextFromDoc($doc)
    {
        try {
            return 'محتوى مستند Word - يتطلب مكتبة استخراج النص';
        } catch (\Exception $e) {
            return 'لا يمكن استخراج النص من المستند';
        }
    }

    /**
     * بناء prompt شامل للذكاء الاصطناعي
     */
    private function buildComprehensivePrompt($question, $baseContent, $attachmentAnalysis)
    {
        $prompt = "أنت مساعد تعليمي ذكي. مهمتك الإجابة على السؤال التالي بناءً على المحتوى التعليمي والمرفقات المقدمة.\n\n";
        
        if (!empty($baseContent)) {
            $prompt .= "المحتوى التعليمي الأساسي:\n" . $baseContent . "\n\n";
        }
        
        if (!empty($attachmentAnalysis)) {
            $prompt .= "تحليل المرفقات:\n" . $attachmentAnalysis . "\n\n";
        }
        
        $prompt .= "السؤال: " . $question . "\n\n";
        $prompt .= "يرجى تقديم إجابة شاملة ودقيقة باللغة العربية، مع الاستفادة من المحتوى التعليمي والمرفقات المقدمة.";
        
        return $prompt;
    }

    /**
     * استدعاء Gemini API
     */
    private function callGeminiAPI($prompt, $maxTokens = 50)
    {
        $apiKey = env('GEMINI_API_KEY');
        if (empty($apiKey)) {
            return 'فشل في الاتصال بخدمة التحليل. يرجى المحاولة لاحقاً.';
        }

        $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=' . $apiKey;
        
        $payload = [
            'contents' => [['parts' => [['text' => $prompt]]]],
            'generationConfig' => ['temperature' => 0.3, 'maxOutputTokens' => $maxTokens],
        ];

        try {
            $response = Http::timeout(25)->post($url, $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                return $responseData['candidates'][0]['content']['parts'][0]['text'] ?? null;
            }

            return 'فشل في الاتصال بخدمة التحليل. يرجى المحاولة لاحقاً.';
        } catch (\Exception $e) {
            \Log::error('Gemini API error: ' . $e->getMessage());
            return 'فشل في الاتصال بخدمة التحليل. يرجى المحاولة لاحقاً.';
        }
    }

    /**
     * Delete a specific attachment
     */
    public function deleteAttachment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'attachment_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        try {
            $media = Media::findOrFail($request->attachment_id);
            $question = $media->model;

            // Authorization Check
            if (Auth::id() !== $question->user_id && Auth::user()->role_id !== 1) {
                return response()->json(['success' => false, 'message' => 'غير مصرح لك بحذف هذا المرفق.'], 403);
            }

            $media->delete();

            return response()->json(['success' => true, 'message' => 'تم حذف المرفق بنجاح.']);

        } catch (\Exception $e) {
            \Log::error('Error deleting attachment: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'فشل حذف المرفق.'], 500);
        }
    }
}
