<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;
use Laravel\Sanctum\PersonalAccessToken;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class MediaController extends Controller
{
    // public function show($id)
    // {
    //     // Ensure the user is authenticated

    //     if (!request()->token && !Auth::check()) {
    //         return response('You must login', 401);
    //     }

    //     if (request()->token) {
    //         $user = Auth::user();

    //         if (!$user) {
    //             return response('Unauthorized', 403);
    //         }
    //         if ($user->token !== request()->token) {
    //             return response('Unauthorized', 403);
    //         }
    //     }

    //     $media = Media::findOrFail($id);

    //     // Get the path to the media file
    //     $path = $media->getPath();

    //     // Return the media file as a response
    //     return Response::file($path);
    // }

    public function show($id)
    {
        // التحقق من التوكن أو تسجيل الدخول
        if (!request()->token && !Auth::check()) {
            return response('You must login', 401);
        }

        if (request()->token) {
            // جلب التوكن من الطلب
            $token = request()->token;

            // محاولة العثور على التوكن في جدول personal_access_tokens
            $accessToken = PersonalAccessToken::findToken($token);

            if (!$accessToken) {
                return response('Unauthorized', 403);
            }

            // التأكد من أن التوكن يخص المستخدم المالك للملف
            $user = $accessToken->tokenable;
            if (!$user || !$user instanceof \App\Models\User) {
                return response('Unauthorized', 403);
            }

            // تسجيل دخول المستخدم إذا كان التوكن صحيحًا
            Auth::setUser($user);
        }

        // العثور على الميديا بناءً على المعرف
        $media = Media::findOrFail($id);

        // جلب المسار الخاص بالملف
        $path = $media->getPath();

        // إرجاع الملف كاستجابة
        return Response::file($path);
    }

    public function web_show($id)
    {
        if (!Auth::check()) {
            return redirect()->url('/');
        }

        $media = Media::findOrFail($id);
        $path = $media->getPath();

        if (!file_exists($path)) {
            abort(404, 'File not found.');
        }

        return response()->file($path);
    }
}
