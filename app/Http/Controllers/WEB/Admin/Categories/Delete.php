<?php

namespace App\Http\Controllers\WEB\Admin\Categories;

use App\Services\CategoryService;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class Delete extends Component
{
    use LivewireAlert;

    public $showDeleteModel = false;
    public $showRestoreModel = false;
    public $showForceDeleteModel = false;
    public $showActivateVisibilityModel = false;
    public $showDeactivateVisibilityModel = false;
    public $showActivateVisibilityModel_student = false;
    public $showDeactivateVisibilityModel_student = false;
    public $itemId;
    protected $CategoryService;

    public function __construct()
    {
        $this->CategoryService = new CategoryService();
    }
    protected $listeners = ['showDeleteModel', 'showRestoreModel', 'showForceDeleteModel', 'showActivateVisibilityModel', 'showDeactivateVisibilityModel', 'showActivateVisibilityModel_student', 'showDeactivateVisibilityModel_student'];

    public function showDeleteModel($itemId)
    {
        $this->itemId = $itemId;
        $this->showDeleteModel = true;
    }

    public function closeDeleteModel()
    {
        $this->showDeleteModel = false;
        $this->reset();
    }

    public function delete()
    {
        $deleted = $this->CategoryService->deleteCategory($this->itemId);
        if ($deleted->isSuccess) {
            $this->reset();
            $this->closeDeleteModel();
            $this->emit('refreshParent');
            $this->alert('success', __('Deleted successfully'));
        }
    }

    public function showRestoreModel($itemId)
    {
        $this->itemId = $itemId;
        $this->showRestoreModel = true;
    }

    public function closeRestoreModel()
    {
        $this->showRestoreModel = false;
        $this->reset();
    }

    public function restore()
    {
        $restore = $this->CategoryService->restoreCategory($this->itemId);
        if ($restore->isSuccess) {
            $this->reset();
            $this->closeRestoreModel();
            $this->emit('refreshParent');
            $this->alert('success', __('Restored successfully'));
        }
    }

    public function showForceDeleteModel($itemid)
    {
        $this->itemId = $itemid;
        $this->showForceDeleteModel = true;
    }

    public function closeForceDeleteModel()
    {
        $this->showForceDeleteModel = false;
        $this->reset();
    }

    public function forceDelete()
    {
        $forceDelete = $this->CategoryService->forceDeleteCategory($this->itemId);
        if ($forceDelete->isSuccess) {
            $this->reset();
            $this->closeForceDeleteModel();
            $this->alert('success', __('Deleted successfully'));
            $this->emit('refreshParent');
        }
    }

    public function showActivateVisibilityModel($itemid)
    {
        $this->itemId = $itemid;
        $this->showActivateVisibilityModel = true;
    }

    public function closeActivateVisibilityModel()
    {
        $this->showActivateVisibilityModel = false;
        $this->reset();
    }

    public function showActivateVisibilityModel_student($itemid)
    {
        $this->itemId = $itemid;
        $this->showActivateVisibilityModel_student = true;
    }

    public function closeActivateVisibilityModel_student()
    {
        $this->showActivateVisibilityModel_student = false;
        $this->reset();
    }

    public function ActivateVisibility()
    {
        $status = $this->CategoryService->changePageStatus($this->itemId, true);
        if ($status->isSuccess) {
            $this->reset();
            $this->closeActivateVisibilityModel();
            $this->alert('success', __('The visibility status has been successfully changed'));
            $this->emit('refreshParent');
        }
    }

    public function ActivateVisibility_student()
    {
        $status = $this->CategoryService->changePageStatus_student($this->itemId, true);
        if ($status->isSuccess) {
            $this->reset();
            $this->closeActivateVisibilityModel_student();
            $this->alert('success', __('The visibility status has been successfully changed'));
            $this->emit('refreshParent');
        }
    }

    public function showDeactivateVisibilityModel($itemid)
    {
        $this->itemId = $itemid;
        $this->showDeactivateVisibilityModel = true;
    }

    public function closeDeactivateVisibilityModel()
    {
        $this->showDeactivateVisibilityModel = false;
        $this->reset();
    }

    public function showDeactivateVisibilityModel_student($itemid)
    {
        $this->itemId = $itemid;
        $this->showDeactivateVisibilityModel_student = true;
    }

    public function closeDeactivateVisibilityModel_student()
    {
        $this->showDeactivateVisibilityModel_student = false;
        $this->reset();
    }

    public function DeactivateVisibility()
    {
        $status = $this->CategoryService->changePageStatus($this->itemId, false);
        if ($status->isSuccess) {
            $this->reset();
            $this->closeDeactivateVisibilityModel();
            $this->alert('success', __('The visibility status has been successfully changed'));
            $this->emit('refreshParent');
        }
    }

    public function DeactivateVisibility_student()
    {
        $status = $this->CategoryService->changePageStatus_student($this->itemId, false);
        if ($status->isSuccess) {
            $this->reset();
            $this->closeDeactivateVisibilityModel_student();
            $this->alert('success', __('The visibility status has been successfully changed'));
            $this->emit('refreshParent');
        }
    }

    public function render()
    {
        return view('livewire.admin.categories.delete');
    }
}
