<?php

namespace App\Http\Controllers\WEB\Admin\Categories;

use App\Http\Controllers\WEB\BaseLivewireComponent;
use App\Services\CategoryService;

class Index extends BaseLivewireComponent
{
    protected $paginationTheme = 'bootstrap';
    protected $CategoryService;
    public ?string $name = null;

    public function __construct()
    {
        $this->CategoryService = new CategoryService();
    }
    protected $listeners = ['refreshParent' => '$refresh'];

    public function updatingTerm()
    {
        parent::updatingTerm();
    }

    public function updatingOrderBy()
    {
        parent::updatingOrderBy();
    }

    public function updatingSortBy()
    {
        parent::updatingSortBy();
    }

    public function updatingperPage()
    {
        parent::updatingperPage();
    }

    public function updatingTrashed()
    {
        parent::updatingTrashed();
    }

    public function toggleTrashed()
    {
        parent::toggleTrashed();
    }

    public function selectedItem($action, $itemId = null)
    {
        if ($action == 'create') {
            $this->emit('showCreateModel');
        } elseif ($action == 'update') {
            $this->emit('showUpdateModel', $itemId);
        } elseif ($action == 'delete') {
            $this->emit('showDeleteModel', $itemId);
        } elseif ($action == 'restore') {
            $this->emit('showRestoreModel', $itemId);
        } elseif ($action == 'forceDelete') {
            $this->emit('showForceDeleteModel', $itemId);
        } elseif ($action == 'activate') {
            $this->emit('showActivateVisibilityModel', $itemId);
        } elseif ($action == 'deactivate') {
            $this->emit('showDeactivateVisibilityModel', $itemId);
        } elseif ($action == 'activate_student') {
            $this->emit('showActivateVisibilityModel_student', $itemId);
        } elseif ($action == 'deactivate_student') {
            $this->emit('showDeactivateVisibilityModel_student', $itemId);
        }
    }

    public function getItem()
    {
        if (!empty($this->term)) {
            return $this->CategoryService->searchCategories($this->term, $this->perPage)->data;
        } else {
            return $this->CategoryService->allCategories($this->perPage, $this->trashed, $this->orderBy, $this->sortBy)->data;
        }
    }

    public function render()
    {
        return view('livewire.admin.categories.index', ['categories' => $this->getItem()])->layout('layouts.app');
    }
}
