<?php

namespace App\Http\Controllers\WEB\Admin\Content;

use App\Services\CategoryService;
use App\Services\ContentService;
use Illuminate\Support\Facades\App;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithFileUploads;

class Create extends Component
{
    use LivewireAlert;
    use WithFileUploads;

    protected $ContentService;
    protected $CategoryService;
    public $content = [
        'class' => '',
        'teacher' => '',
        'student' => '',
    ];
    public $classMedia;
    public $teacherMedia;
    public $studentMedia;
    public $categories;
    public $category;
    public $has_ai_analysis = false;
    protected $listeners = ['updateContent'];

    public function __construct()
    {
        $this->ContentService = new ContentService();
        $this->CategoryService = new CategoryService();
        $categoriesResult = $this->CategoryService->index();
        if ($categoriesResult->isSuccess) {
            $this->categories = $categoriesResult->data;
        } else {
            $this->categories = collect([]);
        }
        $this->dispatchBrowserEvent('fileUploaded');
    }

    public function updateContent($data)
    {
        $id = $data['id'];
        $content = $data['content'];

        // تعيين القيمة المناسبة
        if ($id === '#content\\.class') {
            $this->content['class'] = $content;
        } elseif ($id === '#content\\.teacher') {
            $this->content['teacher'] = $content;
        } elseif ($id === '#content\\.student') {
            $this->content['student'] = $content;
        }
    }

    // public function mount()
    // {
    //     $this->content = [
    //         'class' => '',
    //         'teacher' => '',
    //         'student' => '',
    //     ];
    // }

    // public function updateContent($key, $value)
    // {
    //     // تحديث قيمة المحتوى بناءً على المفتاح والبيانات
    //     if (array_key_exists($key, $this->content)) {
    //         $this->content[$key] = $value;
    //     }
    // }

    protected function rules()
    {
        $lang = App::getLocale();

        return [
            'content.'.$lang => 'required',
            'classMedia' => 'nullable|file|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'teacherMedia' => 'nullable|file|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'studentMedia' => 'nullable|file|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ];
    }

    public function create()
    {
        // $this->validate();

        $classMediaPath = $this->classMedia ? $this->classMedia->store('media', 'public') : null;
        $teacherMediaPath = $this->teacherMedia ? $this->teacherMedia->store('media', 'public') : null;
        $studentMediaPath = $this->studentMedia ? $this->studentMedia->store('media', 'public') : null;

        $data = [
            'category_id' => $this->category,
            'has_ai_analysis' => $this->has_ai_analysis,
            'content' => $this->content,
            'classMedia' => $classMediaPath,
            'teacherMedia' => $teacherMediaPath,
            'studentMedia' => $studentMediaPath,
        ];
        dd($data);
        $created = $this->ContentService->createContent($data);

        if ($created) {
            $this->closeCreateModel();
            $this->alert('success', __('Data Created successfully'));
            $this->emit('refreshParent');
        }
    }

    public function render()
    {
        return view('livewire.admin.content.create')->layout('layouts.app');
    }
}
