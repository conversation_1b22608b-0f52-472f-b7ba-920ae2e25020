<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\AiAnalysis;

class FixImagePaths extends Command
{
    protected $signature = 'images:fix-paths {--dry-run : Preview changes without applying them}';
    protected $description = 'Fix image paths in editor content to use correct storage URLs';

    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        $this->info('🔍 Scanning for image paths to fix...');
        
        $analyses = AiAnalysis::whereNotNull('comprehensive_analysis')->get();
        $fixedCount = 0;
        
        foreach ($analyses as $analysis) {
            $content = $analysis->comprehensive_analysis;
            $originalContent = $content;
            
            // إصلاح المسارات المختلفة للصور
            $patterns = [
                // مسارات storage مباشرة
                '/storage\/images\/converted\/([^"\'>\s]+)/i' => 'storage/images/converted/$1',
                // مسارات app/public
                '/app\/public\/images\/converted\/([^"\'>\s]+)/i' => 'storage/images/converted/$1',
                // مسارات خاطئة أخرى
                '/images\/converted\/([^"\'>\s]+)/i' => 'storage/images/converted/$1',
            ];
            
            foreach ($patterns as $pattern => $replacement) {
                $content = preg_replace($pattern, $replacement, $content);
            }
            
            // التأكد من أن المسارات تبدأ بـ asset() أو URL كامل
            $content = preg_replace_callback(
                '/src=["\'](?!http|\/\/|asset\()(storage\/images\/converted\/[^"\']+)["\']/i',
                function($matches) {
                    return 'src="' . asset($matches[1]) . '"';
                },
                $content
            );
            
            if ($content !== $originalContent) {
                $fixedCount++;
                
                if ($isDryRun) {
                    $this->line("📝 Would fix analysis ID: {$analysis->id}");
                } else {
                    $analysis->update(['comprehensive_analysis' => $content]);
                    $this->line("✅ Fixed analysis ID: {$analysis->id}");
                }
            }
        }
        
        if ($isDryRun) {
            $this->info("🔍 Preview complete. Found {$fixedCount} analyses that need fixing.");
            $this->info("Run without --dry-run to apply changes.");
        } else {
            $this->info("✅ Fixed {$fixedCount} analyses with image path issues.");
        }
        
        return 0;
    }
}