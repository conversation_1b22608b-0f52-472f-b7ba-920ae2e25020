<?php

namespace App\Services;

use App\Enums\StatusCodes;
use App\Helpers\ServiceResult;
use App\Models\AiPermission;
use App\Models\AiUsageLog;
use App\Models\Category;
use App\Models\Content;
use App\Models\QuestionAnswer;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ContentService extends BaseService
{
    use AuthorizesRequests;

    protected ImageService $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    // public function createContent(array $data, array $files): ServiceResult
    // {
    //     $serviceResult = new ServiceResult();
    //     $serviceResult->isSuccess = false;
    //     $serviceResult->data = [];
    //     $serviceResult->status = StatusCodes::OPERATION_FAILED;

    //     DB::beginTransaction();

    //     try {
    //         // إنشاء محتوى جديد باستخدام البيانات المقدمة
    //         $content = Content::create($data);

    //         // إضافة ملفات الميديا إلى مجموعات مختلفة باستخدام مكتبة Spatie MediaLibrary
    //         if (isset($files['classMedia'])) {
    //             $content->addMedia($files['classMedia'])->toMediaCollection('class_media');
    //         }

    //         if (isset($files['teacherMedia'])) {
    //             $content->addMedia($files['teacherMedia'])->toMediaCollection('teacher_media');
    //         }

    //         if (isset($files['studentMedia'])) {
    //             $content->addMedia($files['studentMedia'])->toMediaCollection('student_media');
    //         }

    //         // التحقق مما إذا تم إنشاء المحتوى بنجاح
    //         if ($content) {
    //             $serviceResult->isSuccess = true;
    //             $serviceResult->data = $content;
    //             $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
    //         }

    //         DB::commit();
    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         // يمكنك تسجيل الخطأ إذا لزم الأمر
    //     }

    //     return $serviceResult;
    // }
    public function createContent(array $data): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        DB::beginTransaction();
        // dd($data['category_id']);
        $cat = Category::find($data['category_id']);
        $existContent = $cat->contents()->first();
        if (!$existContent) {
            try {
                $content = Content::create($data);

                if ($content) {
                    $serviceResult->isSuccess = true;
                    $serviceResult->data = $content;
                    $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
            }
        } else {
            try {
                $existContent->fill($data);

                if ($existContent->save()) {
                    $serviceResult->isSuccess = true;
                    $serviceResult->data = $existContent;
                    $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
                } else {
                    $serviceResult->status = StatusCodes::OPERATION_FAILED;
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
            }
        }

        return $serviceResult;
    }

    public function allContents($itemsPerPage, $trashed = false, $orderBy = 'id', $sortBy = 'asc'): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;
        /**
         * @var Content
         */
        $query = Content::query();

        if ($trashed) {
            $query->onlyTrashed();
        }

        $contents = $query->orderBy($orderBy, $sortBy)
            ->paginate($itemsPerPage);

        $serviceResult->isSuccess = true;
        $serviceResult->data = $contents;
        $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

        return $serviceResult;
    }

    public function singleContent($contentId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $content = Content::findOrFail($contentId);

        if ($content) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $content;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function searchContents($term, $itemsPerPage): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $contents = Content::select()->where(function ($query) use ($term) {
            $query->where('name', 'like', "%$term%");
        })->paginate($itemsPerPage);

        if ($contents->count() > 0) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $contents;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function updateContent($contentId, $contentData)
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        DB::beginTransaction();

        try {
            $content = Content::findOrFail($contentId);

            $content->fill($contentData);

            if ($content->save()) {
                $serviceResult->isSuccess = true;
                $serviceResult->data = $content;
                $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
            } else {
                $serviceResult->status = StatusCodes::OPERATION_FAILED;
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
        }

        return $serviceResult;
    }

    public function deleteContent($contentId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $content = Content::findOrFail($contentId);
        // $this->authorize('delete', $content);

        if (!$content) {
            $serviceResult->status = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        if ($content->delete()) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $content;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function restoreContent($contentId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $content = Content::onlyTrashed()->findOrFail($contentId);
        // $this->authorize('restore', $content);

        if (!$content) {
            $serviceResult->status = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        if ($content->restore()) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $content;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function forceDeleteContent($contentId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $content = Content::onlyTrashed()->findOrFail($contentId);
        // $this->authorize('forceDelete', $content);

        if (!$content) {
            $serviceResult->status = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        if ($content->forceDelete()) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $content;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    /**
     * إنشاء محتوى جديد مع معالجة الصور بكفاءة.
     */
    public function createContentWithMedia(array $data, array $files = []): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        DB::beginTransaction();

        try {
            $cat = Category::find($data['category_id']);
            $existContent = $cat->contents()->first();

            if (!$existContent) {
                $content = Content::create($data);
            } else {
                $content = $existContent;
                $content->fill($data);
                $content->save();
            }

            // معالجة الملفات المرفوعة
            if (!empty($files)) {
                $this->processMediaFiles($content, $files);
            }

            $serviceResult->isSuccess = true;
            $serviceResult->data = $content->load('media');
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Content creation error: '.$e->getMessage());
        }

        return $serviceResult;
    }

    /**
     * معالجة مفات الوسائط.
     */
    private function processMediaFiles(Content $content, array $files): void
    {
        foreach ($files as $collectionName => $fileArray) {
            if (!is_array($fileArray)) {
                $fileArray = [$fileArray];
            }

            foreach ($fileArray as $file) {
                if ($file instanceof UploadedFile) {
                    // التحقق من صحة الصورة
                    $validationErrors = $this->imageService->validateImage($file);
                    if (!empty($validationErrors)) {
                        throw new \Exception(implode(', ', $validationErrors));
                    }

                    // إضافة الملف إلى المجموعة المناسبة
                    $content->addMedia($file)->toMediaCollection($collectionName);
                }
            }
        }
    }

    // For Apis

    public function contentsIndex($category, $itemsPerPage, $orderBy = 'id', $sortBy = 'asc'): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;
        /**
         * @var Content
         */
        $query = Content::query();
        if ($category) {
            $query = $query->where('category_id', $category);
        }

        $contents = $query->orderBy($orderBy, $sortBy)
            ->paginate($itemsPerPage);
        $serviceResult->isSuccess = true;
        $serviceResult->data = $contents;
        $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

        return $serviceResult;
    }

    protected function checkPermission(string $permission): bool
    {
        $user = Auth::user();

        // استثناء المستخدم ذو الرول رقم 1
        if ($user->role_id === 1) {
            return true;
        }

        // التحقق من صلاحيات المعلم
        if ($user->type === 2) {
            $permissions = AiPermission::where(function ($query) use ($user) {
                $query->where('teacher_id', $user->id)
                    ->orWhere('school_id', $user->school_id);
            })->first();

            if ($permissions && $permissions->$permission) {
                return true;
            }
        }

        return false;
    }

    /**
     * الحصول على جميع أسئلة المحتوى.
     */
    public function getContentQuestions($contentId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        try {
            if (!$this->checkPermission('can_view_questions')) {
                throw new \Exception('ليس لديك صلاحية لعرض الأسئلة');
            }

            $content = Content::findOrFail($contentId);

            $user = Auth::user();
            if ($user->type === 1) {
                $questions = $content->questionsAnswers()->with('media')->orderBy('created_at', 'desc')->get();
            } else {
                $questions = $content->questionsAnswers()
                    ->with('media')
                    ->where(function ($query) use ($user) {
                        $query->where('status', 1)
                              ->orWhere('user_id', $user->id);
                    })
                    ->orderBy('created_at', 'desc')
                    ->get();
            }

            $serviceResult->isSuccess = true;
            $serviceResult->data = $questions;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } catch (\Exception $e) {
            $serviceResult->message = $e->getMessage();
        }

        return $serviceResult;
    }

    /**
     * إضافة سؤال جديد.
     */
    public function addQuestion($contentId, $data, $attachment = null): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        DB::beginTransaction();

        try {
            if (!$this->checkPermission('can_add_questions')) {
                throw new \Exception('ليس لديك صلاحية لإضافة الأسئلة');
            }

            $user = Auth::user();
            if ($user->type === 2) { // معلم
                $permission = AiPermission::where(function ($query) use ($user) {
                    $query->where('teacher_id', $user->id)
                        ->orWhere('school_id', $user->school_id);
                })->first();

                if ($permission && $permission->questions_limit > 0) {
                    if (!AiUsageLog::canPerformAction($user->id, 'add_questions', $permission->questions_limit)) {
                        $todayUsage = AiUsageLog::getTodayUsage($user->id, 'add_questions');
                        throw new \Exception("تم استنفاد الحد اليومي ({$todayUsage}/{$permission->questions_limit})");
                    }
                }
            }

            $content = Content::findOrFail($contentId);
            $isAiGenerated = isset($data['is_ai_generated']) ? (bool) $data['is_ai_generated'] : false;

            // تحسين فحص الارتباط
            if (!$isAiGenerated) {
                $analysisResult = $this->analyzeQuestionRelevance($contentId, $data['question'], $attachment);
                if (!$analysisResult['is_relevant']) {
                    \Log::warning('Question rejected as irrelevant', [
                        'question' => $data['question'],
                        'reason' => $analysisResult['reason'],
                        'ai_response' => $analysisResult['ai_response'] ?? null,
                    ]);
                    throw new \Exception('السؤال غير مخصص لهذا المحتوى. يرجى إضافة سؤال متعلق بالمحتوى التعليمي المعروض.');
                }

                \Log::info('Question accepted as relevant', [
                    'question' => $data['question'],
                    'reason' => $analysisResult['reason'],
                ]);
            }

            $questionData = array_merge($data, [
                'user_id' => $user->id,
                'content_id' => $contentId,
                'difficulty' => $data['difficulty'] ?? 'متوسط',
                'type' => $data['type'] ?? 'عام',
                'status' => $user->type === 2 ? 0 : 1, // المعلمون: غير مفعل، الأدمن: مفعل
                'is_ai_generated' => $isAiGenerated,
            ]);

            $question = $content->questionsAnswers()->create($questionData);

            // معالجة المرفق إذا كان موجوداً
            if ($attachment) {
                $question->addMedia($attachment)->toMediaCollection('question_attachments');
            }

            if ($user->type === 2) { // معلم
                AiUsageLog::logUsage($user->id, 'add_questions');
            }

            $serviceResult->isSuccess = true;
            $serviceResult->data = $question->fresh()->load('media'); // تحميل الوسائط مع السؤال
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $serviceResult->message = $e->getMessage();
        }

        return $serviceResult;
    }

    /**
     * تعديل سؤال موجود.
     */
    public function editQuestion($questionId, $data, $attachment = null): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        DB::beginTransaction();

        try {
            if (!$this->checkPermission('can_edit_questions')) {
                throw new \Exception('ليس لديك صلاحية لتعديل الأسئلة');
            }

            $question = QuestionAnswer::findOrFail($questionId);
            $question->update($data);

            $serviceResult->isSuccess = true;
            $serviceResult->data = $question->fresh();
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $serviceResult->message = $e->getMessage();
        }

        return $serviceResult;
    }

    /**
     * حذف سؤال.
     */
    public function deleteQuestion($questionId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        DB::beginTransaction();

        try {
            if (!$this->checkPermission('can_delete_questions')) {
                throw new \Exception('ليس لديك صلاحية لحذف الأسئلة');
            }

            $question = QuestionAnswer::findOrFail($questionId);
            $question->delete();

            $serviceResult->isSuccess = true;
            $serviceResult->data = null;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $serviceResult->message = $e->getMessage();
        }

        return $serviceResult;
    }

    /**
     * توليد إجابة بالذكاء الاصطناعي.
     */
    public function ai_answer($questionId): ServiceResult
    {
        $serviceResult = new ServiceResult();

        try {
            if (!$this->checkPermission('can_generate_answers')) {
                throw new \Exception('ليس لديك صلاحية لتوليد الإجابات');
            }

            $question = QuestionAnswer::with('media')->findOrFail($questionId);

            $promptParts = [];
            $promptParts[] = ['text' => 'أنت مساعد تعليمي خبير. أجب عن السؤال التالي بناءً على المعلومات المقدمة (نص و/أو صور/ملفات). كن دقيقاً ومباشراً في إجابتك.\n\nالسؤال: '.$question->question];

            // معالجة المرفقات
            $attachments = $question->getMedia('question_attachments');
            foreach ($attachments as $attachment) {
                $attachmentData = $this->getAttachmentDataForAI($attachment);
                if ($attachmentData) {
                    $promptParts[] = ['inline_data' => $attachmentData];
                }
            }

            $payload = [
                'contents' => ['parts' => $promptParts],
                'generationConfig' => ['temperature' => 0.3, 'maxOutputTokens' => 800],
            ];

            $answer = $this->callGeminiAPIWithPayload($payload, 45);

            if ($answer && !str_contains($answer, 'فشل في الاتصال')) {
                $question->update(['answer' => $answer]);
                $serviceResult->isSuccess = true;
                $serviceResult->data = $question->fresh();
                $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
            } else {
                throw new \Exception('فشل في توليد الإجابة من الذكاء الاصطناعي.');
            }
        } catch (\Exception $e) {
            $serviceResult->isSuccess = false;
            $serviceResult->message = $e->getMessage();
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    /**
     * Helper to get attachment data for AI analysis.
     */
    private function getAttachmentDataForAI(Media $media): ?array
    {
        try {
            $path = $media->getPath();
            if (!file_exists($path)) {
                return null;
            }

            return [
                'mime_type' => $media->mime_type,
                'data' => base64_encode(file_get_contents($path)),
            ];
        } catch (\Exception $e) {
            \Log::error('Failed to get attachment data for AI', ['media_id' => $media->id, 'error' => $e->getMessage()]);

            return null;
        }
    }

    /**
     * استدعاء Gemini API مع payload.
     */
    private function callGeminiAPIWithPayload($payload, $timeout = 25, $retries = 2)
    {
        $apiKey = env('GEMINI_API_KEY');
        $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key='.$apiKey;

        for ($attempt = 1; $attempt <= $retries; ++$attempt) {
            try {
                $response = \Illuminate\Support\Facades\Http::timeout($timeout)->post($url, $payload);

                if ($response->successful()) {
                    $responseData = $response->json();

                    return $responseData['candidates'][0]['content']['parts'][0]['text'] ?? null;
                }

                $error = $response->json();

                if (isset($error['error']['code']) && $error['error']['code'] == 429) {
                    \Log::error('Gemini API quota exceeded: '.json_encode($error));

                    return 'تم استنفاد الحد اليومي لخدمة التحليل. يرجى المحاولة غداً.';
                }

                if (isset($error['error']['code']) && $error['error']['code'] == 503) {
                    \Log::warning('Gemini API overloaded: '.json_encode($error));
                    if ($attempt < $retries) {
                        sleep(2);
                        continue;
                    }

                    return 'خدمة الذكاء الاصطناعي مشغولة حالياً. يرجى المحاولة مرة أخرى بعد قليل.';
                }

                \Log::warning('Gemini API failed: '.json_encode($error));

                return 'فشل في الاتصال بخدمة التحليل.';
            } catch (\Exception $e) {
                \Log::error("Gemini API exception attempt {$attempt}: ".$e->getMessage());
                if ($attempt < $retries) {
                    sleep(1);
                    continue;
                }

                return 'خطأ في الاتصال بخدمة التحليل.';
            }
        }

        return 'فشل في الاتصال بخدمة التحليل.';
    }

    /**
     * تحليل مدى ارتباط السؤال بالمحتوى التعليمي باستخدام نفس منطق AIAnalysisController.
     */
    private function analyzeQuestionRelevance($contentId, $question, $attachments = null)
    {
        \Log::info('Starting question relevance analysis', [
            'content_id' => $contentId,
            'question' => $question,
            'has_attachments' => !empty($attachments),
        ]);

        try {
            $content = Content::with('aiAnalysis')->find($contentId);
            if (!$content) {
                \Log::error('Content not found', ['content_id' => $contentId]);
                return ['is_relevant' => false, 'reason' => 'المحتوى غير موجود'];
            }

            // جلب المحتوى من مصادر متعددة (نفس منطق AIAnalysisController)
            $textContent = $this->getContentForAnalysis($content);

            // إذا لم يكن هناك محتوى، نقبل السؤال
            if (empty(trim($textContent))) {
                \Log::info('No content found for analysis, accepting question');
                return ['is_relevant' => true, 'reason' => 'لا يوجد محتوى للمقارنة'];
            }

            // تحليل المرفقات إذا كانت موجودة
            $attachmentAnalysis = '';
            if ($attachments) {
                $attachmentAnalysis = $this->analyzeAttachmentsForRelevance($attachments);

                // إذا كان هناك مرفقات، نكون أكثر تساهلاً لأن المرفقات قد تحتوي على سياق إضافي
                \Log::info('Question has attachments, being more lenient in analysis');
            }

            // إنشاء prompt محسن
            $prompt = $this->buildRelevancePrompt($textContent, $question, $attachmentAnalysis);

            // استدعاء API بشكل صحيح
            $payload = [
                'contents' => [['parts' => [['text' => $prompt]]]],
                'generationConfig' => ['temperature' => 0.3, 'maxOutputTokens' => 50],
            ];

            \Log::info('Sending prompt to AI', ['prompt_length' => strlen($prompt)]);
            $response = $this->callGeminiAPIWithPayload($payload, 25);
            \Log::info('AI response received', ['response' => $response]);

            if ($response && !str_contains($response, 'فشل في الاتصال')) {
                // تحسين منطق التحليل ليكون أكثر تساهلاً
                $responseLower = strtolower(trim($response));

                // إذا كانت الإجابة تحتوي على "نعم" أو لم تحتوي على "لا" بوضوح، نقبل السؤال
                $isRelevant = (strpos($responseLower, 'نعم') !== false) ||
                             (strpos($responseLower, 'لا') === false && !empty($response));

                \Log::info('Analysis completed', [
                    'is_relevant' => $isRelevant,
                    'ai_response' => $response,
                    'analysis_logic' => 'متساهل - يقبل إلا إذا كان الرفض واضحاً',
                ]);

                return [
                    'is_relevant' => $isRelevant,
                    'reason' => $isRelevant ? 'السؤال مرتبط بالمحتوى' : 'السؤال غير مرتبط بالمحتوى',
                    'ai_response' => $response,
                ];
            }

            // في حالة فشل التحليل، نقبل السؤال لتجنب منع المستخدمين
            \Log::warning('AI analysis failed, accepting question by default');
            return ['is_relevant' => true, 'reason' => 'فشل في التحليل - تم قبول السؤال'];

        } catch (\Exception $e) {
            \Log::error('Question relevance analysis error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // في حالة الخطأ، نقبل السؤال لتجنب منع المستخدمين
            return ['is_relevant' => true, 'reason' => 'خطأ في التحليل - تم قبول السؤال'];
        }
    }

    /**
     * جلب المحتوى للتحليل من مصادر متعددة (نفس منطق AIAnalysisController)
     */
    private function getContentForAnalysis(Content $content): string
    {
        $textContent = '';

        // 1. من تحليل الذكاء الاصطناعي الشامل
        if ($content->aiAnalysis) {
            if (!empty($content->aiAnalysis->comprehensive_analysis)) {
                $textContent = strip_tags($content->aiAnalysis->comprehensive_analysis);
            } elseif (!empty($content->aiAnalysis->text_analysis)) {
                $textContent = strip_tags($content->aiAnalysis->text_analysis);
            }

            \Log::info('AI analysis content extracted', [
                'content_length' => strlen($textContent),
                'content_preview' => substr($textContent, 0, 100).'...',
                'has_comprehensive' => !empty($content->aiAnalysis->comprehensive_analysis),
                'has_text_analysis' => !empty($content->aiAnalysis->text_analysis),
            ]);
        }

        // 2. من محتوى الصف (class_media) - نفس منطق AIAnalysisController
        if (empty(trim($textContent))) {
            $description = json_decode($content->description, true);
            if ($description && !empty($description['class'])) {
                $textContent = strip_tags($description['class']);
                \Log::info('Using class content for analysis', [
                    'content_length' => strlen($textContent),
                    'content_preview' => substr($textContent, 0, 100).'...',
                ]);
            }
        }

        // 3. تحليل الصور والفيديوهات من جميع مجموعات الوسائط إذا لم يوجد نص
        if (empty(trim($textContent))) {
            $allMedia = collect();
            $allMedia = $allMedia->merge($content->getMedia('class_media'));
            $allMedia = $allMedia->merge($content->getMedia('teacher_media'));
            $allMedia = $allMedia->merge($content->getMedia('student_media'));

            if ($allMedia->count() > 0) {
                $textContent = 'يحتوي المحتوى على ' . $allMedia->count() . ' ملف وسائط تعليمية (صور، فيديوهات، مستندات)';
                \Log::info('Using all media content description', [
                    'total_media_count' => $allMedia->count(),
                    'class_media' => $content->getMedia('class_media')->count(),
                    'teacher_media' => $content->getMedia('teacher_media')->count(),
                    'student_media' => $content->getMedia('student_media')->count(),
                ]);
            }
        }

        // 4. إذا لم يوجد أي محتوى، استخدم اسم المحتوى والفئة
        if (empty(trim($textContent))) {
            $textContent = 'محتوى تعليمي';
            if (!empty($content->name)) {
                $textContent = $content->name;
            }
            if ($content->category && !empty($content->category->name)) {
                $textContent .= ' - ' . $content->category->name;
            }
            \Log::info('Using content name and category', [
                'content_name' => $content->name,
                'category_name' => $content->category->name ?? 'غير محدد',
            ]);
        }

        return $textContent;
    }

    /**
     * تحليل المرفقات لتقييم الارتباط
     */
    private function analyzeAttachmentsForRelevance($attachments): string
    {
        if (empty($attachments)) {
            return '';
        }

        $analysis = '';

        if (is_array($attachments)) {
            foreach ($attachments as $attachment) {
                $analysis .= $this->analyzeSingleAttachmentForRelevance($attachment) . "\n";
            }
        } else {
            $analysis = $this->analyzeSingleAttachmentForRelevance($attachments);
        }

        return $analysis;
    }

    /**
     * تحليل مرفق واحد
     */
    private function analyzeSingleAttachmentForRelevance($attachment): string
    {
        try {
            $mimeType = $attachment->getMimeType();

            if (strpos($mimeType, 'image/') === 0) {
                // تحليل سريع للصورة
                $imageData = base64_encode(file_get_contents($attachment->getPathname()));

                $payload = [
                    'contents' => [[
                        'parts' => [
                            ['text' => 'صف محتوى هذه الصورة في جملة واحدة قصيرة باللغة العربية.'],
                            ['inline_data' => [
                                'mime_type' => $mimeType,
                                'data' => $imageData
                            ]]
                        ]
                    ]],
                    'generationConfig' => ['temperature' => 0.3, 'maxOutputTokens' => 100]
                ];

                $result = $this->callGeminiAPIWithPayload($payload, 20);
                return $result ?: 'صورة مرفقة';
            } elseif (strpos($mimeType, 'application/pdf') === 0) {
                // استخراج نص من PDF
                try {
                    $parser = new \Smalot\PdfParser\Parser();
                    $pdf = $parser->parseFile($attachment->getPathname());
                    $text = $pdf->getText();
                    return substr(trim($text), 0, 200) . '...';
                } catch (\Exception $e) {
                    return 'ملف PDF مرفق';
                }
            }

            return 'ملف مرفق';
        } catch (\Exception $e) {
            \Log::error('Error analyzing attachment for relevance: ' . $e->getMessage());
            return 'ملف مرفق';
        }
    }

    /**
     * بناء prompt محسن لتقييم الارتباط - أقل حساسية
     */
    private function buildRelevancePrompt(string $textContent, string $question, string $attachmentAnalysis): string
    {
        $prompt = "أنت خبير في تحليل المحتوى التعليمي. مهمتك هي تحديد ما إذا كان السؤال المقدم مرتبطاً بالمحتوى التعليمي أم لا.\n\n";
        $prompt .= "المحتوى التعليمي:\n" . trim($textContent) . "\n\n";

        if (!empty($attachmentAnalysis)) {
            $prompt .= "المرفقات المرسلة مع السؤال:\n" . $attachmentAnalysis . "\n\n";
        }

        $prompt .= 'السؤال المراد تحليله: ' . $question . "\n\n";
        $prompt .= "قم بتحليل ما إذا كان السؤال";

        if (!empty($attachmentAnalysis)) {
            $prompt .= " والمرفقات المرسلة معه";
        }

        $prompt .= " مرتبطاً بالمحتوى التعليمي المقدم.\n";
        $prompt .= "كن متساهلاً في التقييم - اقبل السؤال إذا كان له أي علاقة بالموضوع العام أو المجال التعليمي، حتى لو لم يكن مرتبطاً بشكل مباشر.\n";
        $prompt .= "أجب بـ 'نعم' إذا كان السؤال مرتبطاً بالمحتوى أو بالمجال التعليمي العام، أو 'لا' فقط إذا كان السؤال غير مرتبط تماماً.\n\n";
        $prompt .= 'أجب فقط بكلمة واحدة: نعم أو لا';

        return $prompt;
    }
}
