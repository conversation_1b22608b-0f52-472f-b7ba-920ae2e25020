<?php

namespace App\Services;

use App\Enums\StatusCodes;
use App\Helpers\ServiceResult;
use App\Models\Category;
use App\Models\Content;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Auth;

class Categoryservice extends BaseService
{
    use AuthorizesRequests;

    public function index(): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;
        $query = Category::with(['contents.aiAnalysis']);

        $categories = $query->get();

        if ($categories->count() > 0) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $categories;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function createCategory(array $data): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;
        /**
         * @var Category
         */
        $category = Category::create($data);

        $category->addMedia($data['image'])->toMediaCollection('category');

        if ($category) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $category;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function allCategories($itemsPerPage, $trashed = false, $orderBy = 'id', $sortBy = 'asc'): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;
        /**
         * @var Category
         */
        $query = Category::query()->with(['contents.aiAnalysis']);

        if ($trashed) {
            $query->onlyTrashed();
        }

        $categories = $query->orderBy($orderBy, $sortBy)
            ->paginate($itemsPerPage);

        $serviceResult->isSuccess = true;
        $serviceResult->data = $categories;
        $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

        return $serviceResult;
    }

    public function singleCategory($categoryId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $category = Category::findOrFail($categoryId);

        if ($category) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $category;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function searchCategories($term, $itemsPerPage): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $categories = Category::with(['contents.aiAnalysis'])->where(function ($query) use ($term) {
            $query->where('title', 'like', "%$term%")
                  ->orWhere('description', 'like', "%$term%");
        })->paginate($itemsPerPage);

        if ($categories->count() > 0) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $categories;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function updateCategory($categoryId, $categoryData)
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $category = Category::findOrFail($categoryId);
        if (isset($categoryData['image'])) {
            $category->clearMediaCollection('category');
            $category->addMedia($categoryData['image'])->toMediaCollection('category');
        }
        if (!$category) {
            $serviceResult->status = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        $category->fill($categoryData);

        if ($category->save()) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $category;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function deleteCategory($categoryId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $category = Category::findOrFail($categoryId);
        // $this->authorize('delete', $category);

        if (!$category) {
            $serviceResult->status = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }
        $content = Content::where('category_id', $categoryId)->delete();
        if ($category->delete()) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $category;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function restoreCategory($categoryId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $category = Category::onlyTrashed()->findOrFail($categoryId);
        // $this->authorize('restore', $category);

        if (!$category) {
            $serviceResult->status = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        if ($category->restore()) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $category;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function forceDeleteCategory($categoryId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $category = Category::onlyTrashed()->findOrFail($categoryId);
        // $this->authorize('forceDelete', $category);

        if (!$category) {
            $serviceResult->status = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        if ($category->forceDelete()) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $category;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }
    // For Apis

    public function categoriesIndex($itemsPerPage, $orderBy = 'id', $sortBy = 'asc', $term): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        $user = Auth::user();
        $schoolStatus = $user->school;

        // تحديد النصوص بناءً على حالة المدرسة
        if ($schoolStatus) {
            if ($schoolStatus->status == 0) {
                $schoolStatus->status_text = 'not_active';
            } elseif (now()->greaterThan($schoolStatus->expired_at)) {
                $schoolStatus->status_text = 'subscription_ended';
            } else {
                $schoolStatus->status_text = 'active'; // أو النص الذي يناسب الحالة الفعالة
            }
        }

        $query = Category::query()->where('status', 1)->with(['contents.aiAnalysis']);
        if (auth()->user()->type == 3) {
            $query = $query->where('student_status', 1);
        }

        if (!empty($term)) {
            $query->where(function ($query) use ($term) {
                $query->where('title', 'like', "%$term%")
                    ->orWhere('description', 'like', "%$term%");
            });
        }
        $categories = $query->orderBy($orderBy, $sortBy)->paginate($itemsPerPage);

        $serviceResult->isSuccess = true;
        $serviceResult->data = [
            'schoolStatus' => $schoolStatus,
            'categories' => $categories,
        ];
        $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

        return $serviceResult;
    }

    public function changePageStatus(int $pageId, bool $status): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $page = Category::findOrFail($pageId);
        // $this->authorize('update', $page);

        if (!$page) {
            $serviceResult->status = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        $page->status = $status;

        if ($page->save()) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $page;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function changePageStatus_student(int $pageId, bool $status): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $page = Category::findOrFail($pageId);
        // $this->authorize('update', $page);

        if (!$page) {
            $serviceResult->status = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        $page->student_status = $status;

        if ($page->save()) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $page;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }
}
