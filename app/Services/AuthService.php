<?php

namespace App\Services;

use App\Enums\LoginUsernameType;
use App\Enums\StatusCodes;
use App\Exceptions\VerificationCodeExpiredException;
use App\Exceptions\VerificationCodeNotApprovedException;
use App\Helpers\ServiceResult;
use App\Mail\VerificationCodeMail;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;

class AuthService extends BaseService
{
    public function login(mixed $credentials): ServiceResult
    {
        $serviceResult = new ServiceResult();

        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        if (Auth::attempt([$credentials['type'] => $credentials['credential'], 'password' => $credentials['password']])) {
            /**
             * @var User
             */
            $user = Auth::user();
            
            // التحقق من حالة المدرسة
            $school = $user->school;
            if ($school) {
                $isActive = $school->status == 1 && now()->lessThanOrEqualTo($school->expired_at);
                
                if (!$isActive) {
                    Auth::logout();
                    $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;
                    $serviceResult->message = __('api-response.school_not_active');
                    return $serviceResult;
                }
            }

            /**
             * @var string
             */
            $token = $user->createToken($user->id)->plainTextToken;

            if (isset($credentials['fcm_token'])) {
                $user->fcmTokens()->create(['token' => $credentials['fcm_token']]);
            }

            $serviceResult->data['user'] = $user;
            $serviceResult->data['token'] = $token;

            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

            $serviceResult->isSuccess = true;
        }

        return $serviceResult;
    }

    public function register(mixed $data): ServiceResult
    {
        $serviceResult = new ServiceResult();

        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        /**
         * @var UserService
         */
        $userService = new UserService();

        /**
         * @var ServiceResult
         */
        $userServiceResult = $userService->store($data);

        if ($userServiceResult->isSuccess) {
            /**
             * @var User
             */
            $user = $userServiceResult->data['user'];

            $loginResult = $this->login([
                'type' => LoginUsernameType::USERNAME,
                'credential' => $user->username,
                'password' => $data['password'],
                'fcm_token' => $data['fcm_token'],
            ]
            );

            $serviceResult = $loginResult;
        }

        return $serviceResult;
    }

    public function sendCode(array $credential): ServiceResult
    {
        $serviceResult = new ServiceResult();

        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        /**
         * @var LoginUsernameType
         */
        $credentialType = LoginUsernameType::PHONE;

        $user = User::when(isset($credential['email']), function ($q) use ($credential) {
            return $q->where('email', $credential['email']);
        })
        ->when(isset($credential['phone']), function ($q) use ($credential) {
            return $q->where('phone', $credential['phone']);
        })->first();

        if (isset($credential['email'])) {
            $credentialType = LoginUsernameType::EMAIL;
        }

        $user->code = $this->generateRandomCode(4);
        $user->code_expired_at = Carbon::now()->addMinutes(10);

        $user->save();

        $result = $this->sendNotification($user, $credentialType);

        if ($result) {
            $serviceResult->isSuccess = true;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function verifyCode(array $data)
    {
        $serviceResult = new ServiceResult();

        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $user = User::when(isset($data['email']), function ($q) use ($data) {
            return $q->where('email', $data['email']);
        })
        ->when(isset($data['phone']), function ($q) use ($data) {
            return $q->where('phone', $data['phone']);
        })->first();

        if ($user->code != $data['code']) {
            throw new VerificationCodeNotApprovedException();
        }

        if (!Carbon::now()->lessThan($user->code_expired_at)) {
            throw new VerificationCodeExpiredException();
        }

        /**
         * @var string
         */
        $token = $user->createToken($user->id)->plainTextToken;

        $serviceResult->isSuccess = true;
        $serviceResult->data['token'] = $token;
        $serviceResult->status = StatusCodes::ACCEPTED;

        return $serviceResult;
    }

    public function resetPassword(string $newPassword): ServiceResult
    {
        $serviceResult = new ServiceResult();

        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $user = Auth::user();

        $user->password = Hash::make($newPassword);

        $user->save();

        $serviceResult->isSuccess = true;
        $serviceResult->status = StatusCodes::ACCEPTED;

        return $serviceResult;
    }

    private function generateRandomCode(int $length): string
    {
        return substr(str_shuffle('0123456789'), 0, $length);
    }

    public function sendNotification(User $user, string $type): bool
    {
        try {
            switch ($type) {
                case LoginUsernameType::PHONE :
                    // code...
                    break;

                case LoginUsernameType::EMAIL :
                    Mail::to($user)->send(new VerificationCodeMail($user->code, $user->code_expired_at));

                    return true;
                    break;

                default:
                    return false;
                    break;
            }
        } catch (\Exception $exception) {
        }

        return false;
    }
}
